"""
End-to-end tests for complete trend platform workflow.

Tests the entire pipeline from trend scraping to content generation
to deployment, validating the complete system integration.
"""

import pytest
from unittest.mock import AsyncMock, patch
import asyncio
from datetime import datetime

from scraper.trend_orchestrator import TrendOrchestrator
from generator.core import ContentOrchestrator
from deployment.deployment_orchestrator import DeploymentOrchestrator


@pytest.mark.e2e
@pytest.mark.slow
class TestCompleteWorkflow:
    """End-to-end tests for the complete trend platform workflow."""
    
    @pytest.fixture
    async def trend_orchestrator(self, trend_repo):
        """Create trend orchestrator for testing."""
        config = {
            "scrapers": {
                "google_trends": {
                    "enabled": True,
                    "regions": ["US"],
                    "categories": ["Technology"]
                }
            },
            "processing": {
                "min_search_volume": 100,
                "min_growth_rate": 0.1
            }
        }
        return TrendOrchestrator(trend_repo, config)
    
    @pytest.fixture
    async def content_orchestrator(self, content_repo, trend_repo, mock_openai_client):
        """Create content orchestrator for testing."""
        from generator.ai_services import OpenAIService
        from generator.core import ContentOrchestrator
        
        openai_service = OpenAIService(api_key="test-key")
        openai_service.client = mock_openai_client
        
        return ContentOrchestrator(content_repo, trend_repo)
    
    @pytest.fixture
    async def deployment_orchestrator(self, deployment_repo, trend_repo, content_repo):
        """Create deployment orchestrator for testing."""
        config = {
            "coolify": {
                "api_url": "https://test-coolify.com",
                "api_token": "test-token"
            },
            "dns": {
                "cloudflare_api_token": "test-cf-token",
                "zone_id": "test-zone",
                "base_domain": "trends.example.com"
            }
        }
        
        return DeploymentOrchestrator(
            deployment_repository=deployment_repo,
            trend_repository=trend_repo,
            content_repository=content_repo,
            config=config
        )
    
    @pytest.mark.asyncio
    async def test_complete_trend_pipeline(
        self,
        trend_orchestrator,
        content_orchestrator,
        deployment_orchestrator,
        trend_repo,
        content_repo,
        deployment_repo,
        mock_openai_client
    ):
        """Test complete pipeline: scrape → process → generate → deploy."""
        
        # Step 1: Mock trend scraping
        with patch.object(trend_orchestrator, 'scrape_all_sources') as mock_scrape:
            mock_scrape.return_value = {
                "total_scraped": 5,
                "processed": 3,
                "stored": 2,
                "trends": [
                    {
                        "id": "trend-1",
                        "keyword": "AI Technology",
                        "category": "Technology",
                        "status": "pending"
                    },
                    {
                        "id": "trend-2", 
                        "keyword": "Machine Learning",
                        "category": "Technology",
                        "status": "pending"
                    }
                ]
            }
            
            # Execute scraping
            scrape_result = await trend_orchestrator.scrape_all_sources()
            
            assert scrape_result["stored"] == 2
            assert len(scrape_result["trends"]) == 2
        
        # Step 2: Approve trends (simulate manual approval)
        trend_ids = [trend["id"] for trend in scrape_result["trends"]]
        for trend_id in trend_ids:
            await trend_repo.update(trend_id, {"status": "approved"})
        
        # Step 3: Generate content for approved trends
        mock_openai_client.chat.completions.create.side_effect = [
            # Title generation
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="The Future of AI Technology"))],
                usage=AsyncMock(total_tokens=20)
            ),
            # Content generation
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Comprehensive article about AI technology trends and their impact on modern society..."))],
                usage=AsyncMock(total_tokens=500)
            ),
            # Meta description
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Explore the latest AI technology trends and innovations."))],
                usage=AsyncMock(total_tokens=30)
            ),
            # Repeat for second trend
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Machine Learning Revolution"))],
                usage=AsyncMock(total_tokens=20)
            ),
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="In-depth analysis of machine learning advancements..."))],
                usage=AsyncMock(total_tokens=450)
            ),
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Discover machine learning breakthroughs and applications."))],
                usage=AsyncMock(total_tokens=25)
            )
        ]
        
        content_results = []
        for trend_id in trend_ids:
            result = await content_orchestrator.generate_content_for_trend(trend_id)
            content_results.append(result)
            
            assert result["success"] is True
            assert result["content_id"] is not None
        
        # Verify content was created
        for trend_id in trend_ids:
            content = await content_repo.get_by_trend_id(trend_id)
            assert content is not None
            assert len(content["body"]) > 100  # Substantial content
        
        # Step 4: Deploy approved trends with content
        with patch.object(deployment_orchestrator, '_prepare_git_repository') as mock_git:
            with patch.object(deployment_orchestrator, '_setup_coolify_application') as mock_coolify:
                with patch.object(deployment_orchestrator, '_deploy_to_coolify') as mock_deploy:
                    with patch.object(deployment_orchestrator.deployment_tracker, 'start_deployment_tracking') as mock_track:
                        
                        # Setup deployment mocks
                        mock_git.return_value = {
                            "success": True,
                            "remote_url": "https://github.com/test/repo.git",
                            "commit_sha": "abc123"
                        }
                        
                        mock_coolify.return_value = {
                            "success": True,
                            "application_id": "test-app-id",
                            "default_domain": "test-app.coolify.io"
                        }
                        
                        mock_deploy.return_value = {
                            "success": True,
                            "deployment_id": "test-deployment-id"
                        }
                        
                        mock_track.return_value = True
                        
                        # Deploy all trends
                        deployment_results = []
                        for trend_id in trend_ids:
                            result = await deployment_orchestrator.deploy_trend_site(trend_id)
                            deployment_results.append(result)
                            
                            assert result["success"] is True
                            assert result["deployment_id"] is not None
        
        # Step 5: Verify complete pipeline results
        pipeline_summary = {
            "trends_scraped": scrape_result["stored"],
            "content_generated": len([r for r in content_results if r["success"]]),
            "sites_deployed": len([r for r in deployment_results if r["success"]]),
            "success_rate": 1.0
        }
        
        assert pipeline_summary["trends_scraped"] == 2
        assert pipeline_summary["content_generated"] == 2
        assert pipeline_summary["sites_deployed"] == 2
        assert pipeline_summary["success_rate"] == 1.0
        
        # Verify database state
        for trend_id in trend_ids:
            # Check trend exists and is approved
            trend = await trend_repo.get_by_id(trend_id)
            assert trend["status"] == "approved"
            
            # Check content exists
            content = await content_repo.get_by_trend_id(trend_id)
            assert content is not None
            
            # Check deployment exists
            deployment = await deployment_repo.get_by_trend_id(trend_id)
            assert deployment is not None
            assert deployment["status"] == "pending"  # Would be updated by tracker
    
    @pytest.mark.asyncio
    async def test_workflow_error_recovery(
        self,
        trend_orchestrator,
        content_orchestrator,
        deployment_orchestrator,
        trend_repo,
        mock_openai_client
    ):
        """Test workflow error recovery and partial success handling."""
        
        # Create test trends
        trend_data_1 = {
            "keyword": "Success Trend",
            "slug": "success-trend",
            "category": "Technology",
            "region": "US",
            "source": "test",
            "status": "approved"
        }
        
        trend_data_2 = {
            "keyword": "Failure Trend",
            "slug": "failure-trend", 
            "category": "Technology",
            "region": "US",
            "source": "test",
            "status": "approved"
        }
        
        trend_id_1 = await trend_repo.create(trend_data_1)
        trend_id_2 = await trend_repo.create(trend_data_2)
        
        # Mock content generation with one success, one failure
        mock_openai_client.chat.completions.create.side_effect = [
            # Success for first trend
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Success Title"))], usage=AsyncMock(total_tokens=20)),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Success content"))], usage=AsyncMock(total_tokens=200)),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Success description"))], usage=AsyncMock(total_tokens=30)),
            # Failure for second trend
            Exception("API Error")
        ]
        
        # Generate content for both trends
        result_1 = await content_orchestrator.generate_content_for_trend(trend_id_1)
        result_2 = await content_orchestrator.generate_content_for_trend(trend_id_2)
        
        assert result_1["success"] is True
        assert result_2["success"] is False
        
        # Verify partial success handling
        successful_trends = [trend_id_1]  # Only first trend has content
        
        # Deploy only successful trends
        with patch.object(deployment_orchestrator, '_prepare_git_repository') as mock_git:
            with patch.object(deployment_orchestrator, '_setup_coolify_application') as mock_coolify:
                with patch.object(deployment_orchestrator, '_deploy_to_coolify') as mock_deploy:
                    
                    mock_git.return_value = {"success": True, "commit_sha": "abc123"}
                    mock_coolify.return_value = {"success": True, "application_id": "test-app"}
                    mock_deploy.return_value = {"success": True, "deployment_id": "test-deploy"}
                    
                    batch_result = await deployment_orchestrator.batch_deploy_trends(successful_trends)
                    
                    assert batch_result["total_trends"] == 1
                    assert batch_result["successful"] == 1
                    assert batch_result["failed"] == 0
    
    @pytest.mark.asyncio
    async def test_workflow_performance(
        self,
        trend_orchestrator,
        content_orchestrator,
        deployment_orchestrator,
        trend_repo,
        content_repo,
        mock_openai_client
    ):
        """Test workflow performance with multiple concurrent operations."""
        
        # Create multiple trends
        trend_ids = []
        for i in range(5):
            trend_data = {
                "keyword": f"Performance Trend {i}",
                "slug": f"performance-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "test",
                "status": "approved"
            }
            trend_id = await trend_repo.create(trend_data)
            trend_ids.append(trend_id)
        
        # Mock fast AI responses
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Fast generated content"))],
            usage=AsyncMock(total_tokens=100)
        )
        
        # Measure content generation performance
        start_time = datetime.utcnow()
        
        batch_result = await content_orchestrator.batch_generate_content(
            trend_ids=trend_ids,
            max_concurrent=3
        )
        
        generation_time = (datetime.utcnow() - start_time).total_seconds()
        
        assert batch_result["successful"] == 5
        assert generation_time < 30  # Should complete within 30 seconds
        
        # Verify all content was created
        for trend_id in trend_ids:
            content = await content_repo.get_by_trend_id(trend_id)
            assert content is not None
    
    @pytest.mark.asyncio
    async def test_workflow_data_consistency(
        self,
        trend_repo,
        content_repo,
        deployment_repo
    ):
        """Test data consistency across the workflow."""
        
        # Create trend
        trend_data = {
            "keyword": "Consistency Test",
            "slug": "consistency-test",
            "category": "Technology",
            "region": "US",
            "source": "test",
            "status": "approved"
        }
        trend_id = await trend_repo.create(trend_data)
        
        # Create content
        content_data = {
            "trend_id": trend_id,
            "title": "Test Article",
            "description": "Test description",
            "body": "Test content body",
            "word_count": 100
        }
        content_id = await content_repo.create(content_data)
        
        # Create deployment
        deployment_data = {
            "trend_id": trend_id,
            "content_id": content_id,
            "status": "pending",
            "progress": 0
        }
        deployment_id = await deployment_repo.create(deployment_data)
        
        # Verify relationships
        trend = await trend_repo.get_by_id(trend_id)
        content = await content_repo.get_by_id(content_id)
        deployment = await deployment_repo.get_by_id(deployment_id)
        
        assert trend["id"] == trend_id
        assert content["trend_id"] == trend_id
        assert deployment["trend_id"] == trend_id
        assert deployment["content_id"] == content_id
        
        # Test cascade deletion
        await trend_repo.delete(trend_id)
        
        # Content and deployment should be deleted/updated
        deleted_content = await content_repo.get_by_id(content_id)
        deleted_deployment = await deployment_repo.get_by_id(deployment_id)
        
        assert deleted_content is None  # Should be cascade deleted
        # Deployment might be set to NULL or deleted depending on constraints
    
    @pytest.mark.asyncio
    async def test_workflow_monitoring_and_metrics(
        self,
        trend_orchestrator,
        content_orchestrator,
        deployment_orchestrator
    ):
        """Test workflow monitoring and metrics collection."""
        
        # Get initial metrics
        trend_stats = await trend_orchestrator.get_orchestration_stats()
        content_stats = await content_orchestrator.get_generation_stats()
        deployment_stats = await deployment_orchestrator.get_deployment_metrics()
        
        # Verify metrics structure
        assert "trends_processed" in trend_stats
        assert "content_generated" in content_stats
        assert "orchestrator_stats" in deployment_stats
        
        # All metrics should be non-negative
        assert trend_stats["trends_processed"] >= 0
        assert content_stats["content_generated"] >= 0
        assert deployment_stats["orchestrator_stats"]["deployments_started"] >= 0
