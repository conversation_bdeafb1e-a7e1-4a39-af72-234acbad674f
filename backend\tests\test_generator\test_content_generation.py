"""
Unit tests for content generation functionality.

Tests AI content generation, template rendering, and content processing
for the trend platform content creation system.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from generator.ai_services import AIContentGenerator, OpenAIService
from generator.content_templates import Template<PERSON><PERSON><PERSON>
from generator.core import ContentOrchestrator


@pytest.mark.unit
class TestOpenAIService:
    """Test suite for OpenAI service integration."""
    
    @pytest.fixture
    def openai_service(self, mock_openai_client):
        """Create OpenAI service with mocked client."""
        service = OpenAIService(api_key="test-key")
        service.client = mock_openai_client
        return service
    
    @pytest.mark.asyncio
    async def test_generate_text(self, openai_service, mock_openai_client):
        """Test text generation via OpenAI."""
        prompt = "Write an article about AI trends"
        
        result = await openai_service.generate_text(prompt)
        
        assert result["content"] == "Generated test content"
        assert result["model"] == "gpt-3.5-turbo"
        assert result["tokens_used"] == 100
        
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_image(self, openai_service, mock_openai_client):
        """Test image generation via DALL-E."""
        prompt = "A futuristic AI robot"
        
        result = await openai_service.generate_image(prompt)
        
        assert result["image_url"] == "https://example.com/test-image.jpg"
        assert result["prompt"] == prompt
        
        mock_openai_client.images.generate.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_text_with_parameters(self, openai_service, mock_openai_client):
        """Test text generation with custom parameters."""
        prompt = "Test prompt"
        
        await openai_service.generate_text(
            prompt=prompt,
            max_tokens=500,
            temperature=0.8,
            model="gpt-4"
        )
        
        call_args = mock_openai_client.chat.completions.create.call_args
        assert call_args[1]["max_tokens"] == 500
        assert call_args[1]["temperature"] == 0.8
        assert call_args[1]["model"] == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_error_handling(self, openai_service, mock_openai_client):
        """Test error handling for API failures."""
        mock_openai_client.chat.completions.create.side_effect = Exception("API Error")
        
        with pytest.raises(Exception) as exc_info:
            await openai_service.generate_text("test prompt")
        
        assert "API Error" in str(exc_info.value)


@pytest.mark.unit
class TestAIContentGenerator:
    """Test suite for AI content generation."""
    
    @pytest.fixture
    def content_generator(self, mock_openai_client):
        """Create AI content generator with mocked OpenAI service."""
        openai_service = OpenAIService(api_key="test-key")
        openai_service.client = mock_openai_client
        
        config = {
            "max_article_length": 2000,
            "temperature": 0.7,
            "generate_images": True,
            "generate_code": True
        }
        
        return AIContentGenerator(openai_service, config)
    
    @pytest.mark.asyncio
    async def test_generate_article(self, content_generator, mock_openai_client):
        """Test complete article generation."""
        # Mock multiple OpenAI calls for different content parts
        mock_openai_client.chat.completions.create.side_effect = [
            # Title generation
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="The Future of AI Technology"))],
                usage=AsyncMock(total_tokens=20)
            ),
            # Content generation
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="This is a comprehensive article about AI technology trends..."))],
                usage=AsyncMock(total_tokens=500)
            ),
            # Meta description
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Explore the latest AI technology trends and their impact on society."))],
                usage=AsyncMock(total_tokens=30)
            ),
            # Code snippet
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content='```python\nimport tensorflow as tf\nmodel = tf.keras.Sequential()\n```'))],
                usage=AsyncMock(total_tokens=50)
            )
        ]
        
        result = await content_generator.generate_article(
            keyword="AI Technology",
            category="Technology",
            region="US"
        )
        
        assert result["title"] == "The Future of AI Technology"
        assert "AI technology trends" in result["content"]
        assert result["meta_description"] == "Explore the latest AI technology trends and their impact on society."
        assert result["code_snippet"]["language"] == "python"
        assert "tensorflow" in result["code_snippet"]["code"]
        assert result["word_count"] > 0
    
    @pytest.mark.asyncio
    async def test_generate_hero_image(self, content_generator, mock_openai_client):
        """Test hero image generation."""
        result = await content_generator.generate_hero_image(
            keyword="AI Technology",
            category="Technology",
            article_content="Article about AI trends..."
        )
        
        assert result["image_url"] == "https://example.com/test-image.jpg"
        assert result["alt_text"] is not None
        assert result["prompt"] is not None
        
        mock_openai_client.images.generate.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_code_snippet(self, content_generator, mock_openai_client):
        """Test code snippet generation."""
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content='```javascript\nconst ai = new AIModel();\nai.predict(data);\n```'))],
            usage=AsyncMock(total_tokens=40)
        )
        
        result = await content_generator.generate_code_snippet(
            keyword="JavaScript AI",
            category="Technology",
            content="Article about JavaScript AI libraries"
        )
        
        assert result["language"] == "javascript"
        assert "AIModel" in result["code"]
        assert result["explanation"] is not None
    
    @pytest.mark.asyncio
    async def test_content_validation(self, content_generator):
        """Test content validation and quality checks."""
        # Test with valid content
        valid_content = "This is a comprehensive article with sufficient length and quality content about technology trends."
        
        is_valid = content_generator._validate_content(valid_content, min_length=50)
        assert is_valid is True
        
        # Test with invalid content
        invalid_content = "Too short"
        
        is_valid = content_generator._validate_content(invalid_content, min_length=50)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_content_enhancement(self, content_generator):
        """Test content enhancement and formatting."""
        raw_content = "This is raw content without proper formatting."
        
        enhanced = content_generator._enhance_content(raw_content)
        
        assert len(enhanced) >= len(raw_content)
        # Should add proper formatting, paragraphs, etc.
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, content_generator, mock_openai_client):
        """Test error recovery during content generation."""
        # Mock API failure for first call, success for retry
        mock_openai_client.chat.completions.create.side_effect = [
            Exception("API Error"),
            AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Recovered content"))],
                usage=AsyncMock(total_tokens=50)
            )
        ]
        
        result = await content_generator._generate_with_retry(
            prompt="test prompt",
            max_retries=2
        )
        
        assert result["content"] == "Recovered content"
        assert mock_openai_client.chat.completions.create.call_count == 2


@pytest.mark.unit
class TestTemplateEngine:
    """Test suite for template rendering."""
    
    @pytest.fixture
    def template_engine(self, temp_dir):
        """Create template engine with temporary template directory."""
        # Create test templates
        (temp_dir / "test_template.html").write_text("""
        <html>
        <head><title>{{ title }}</title></head>
        <body>
            <h1>{{ heading }}</h1>
            <p>{{ content }}</p>
        </body>
        </html>
        """)
        
        return TemplateEngine(template_dir=str(temp_dir))
    
    def test_render_template(self, template_engine):
        """Test basic template rendering."""
        context = {
            "title": "Test Page",
            "heading": "Welcome",
            "content": "This is test content"
        }
        
        result = template_engine.render_template("test_template.html", context)
        
        assert "Test Page" in result
        assert "Welcome" in result
        assert "This is test content" in result
    
    def test_render_article_page(self, template_engine):
        """Test article page rendering."""
        # This would test the actual article template
        # For now, test with mock data
        article_data = {
            "title": "Test Article",
            "content": "Article content",
            "meta_description": "Test description",
            "keyword": "test",
            "category": "Technology"
        }
        
        # Would render actual article template
        # result = template_engine.render_article_page(**article_data)
        # assert "Test Article" in result
    
    def test_render_sitemap(self, template_engine):
        """Test sitemap XML rendering."""
        urls = [
            {
                "loc": "https://example.com/",
                "lastmod": "2024-01-01",
                "changefreq": "daily",
                "priority": "1.0"
            },
            {
                "loc": "https://example.com/article",
                "lastmod": "2024-01-01",
                "changefreq": "weekly",
                "priority": "0.8"
            }
        ]
        
        result = template_engine.render_sitemap(urls)
        
        assert "<?xml" in result
        assert "https://example.com/" in result
        assert "https://example.com/article" in result
    
    def test_template_not_found(self, template_engine):
        """Test handling of missing templates."""
        with pytest.raises(Exception):
            template_engine.render_template("nonexistent.html", {})
    
    def test_template_context_validation(self, template_engine):
        """Test template context validation."""
        # Test with missing required context
        incomplete_context = {"title": "Test"}
        
        # Should handle missing variables gracefully
        result = template_engine.render_template("test_template.html", incomplete_context)
        assert "Test" in result


@pytest.mark.integration
class TestContentOrchestrator:
    """Integration tests for content orchestration."""
    
    @pytest.fixture
    def orchestrator(self, content_repo, trend_repo, mock_openai_client):
        """Create content orchestrator with mocked dependencies."""
        openai_service = OpenAIService(api_key="test-key")
        openai_service.client = mock_openai_client
        
        from generator.core import ContentOrchestrator
        return ContentOrchestrator(content_repo, trend_repo)
    
    @pytest.mark.asyncio
    async def test_generate_content_for_trend(self, orchestrator, trend_repo, content_repo, sample_trend_data, mock_openai_client):
        """Test complete content generation workflow."""
        # Create test trend
        trend_id = await trend_repo.create(sample_trend_data)
        
        # Mock AI responses
        mock_openai_client.chat.completions.create.side_effect = [
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Generated Title"))], usage=AsyncMock(total_tokens=20)),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Generated content body"))], usage=AsyncMock(total_tokens=200)),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Generated description"))], usage=AsyncMock(total_tokens=30))
        ]
        
        result = await orchestrator.generate_content_for_trend(trend_id)
        
        assert result["success"] is True
        assert result["content_id"] is not None
        assert result["action"] in ["created", "regenerated"]
        
        # Verify content was stored
        content = await content_repo.get_by_trend_id(trend_id)
        assert content is not None
        assert content["title"] == "Generated Title"
    
    @pytest.mark.asyncio
    async def test_batch_content_generation(self, orchestrator, trend_repo, sample_trend_data, mock_openai_client):
        """Test batch content generation."""
        # Create multiple trends
        trend_ids = []
        for i in range(3):
            trend_data = sample_trend_data.copy()
            trend_data["keyword"] = f"Test Trend {i}"
            trend_data["slug"] = f"test-trend-{i}"
            trend_id = await trend_repo.create(trend_data)
            trend_ids.append(trend_id)
        
        # Mock AI responses
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Generated content"))],
            usage=AsyncMock(total_tokens=100)
        )
        
        result = await orchestrator.batch_generate_content(trend_ids, max_concurrent=2)
        
        assert result["total_trends"] == 3
        assert result["successful"] >= 0
        assert result["failed"] >= 0
        assert result["success_rate"] >= 0
    
    @pytest.mark.asyncio
    async def test_content_regeneration(self, orchestrator, trend_repo, content_repo, sample_trend_data, sample_content_data, mock_openai_client):
        """Test content regeneration for existing content."""
        # Create trend and content
        trend_id = await trend_repo.create(sample_trend_data)
        content_data = sample_content_data.copy()
        content_data["trend_id"] = trend_id
        content_id = await content_repo.create(content_data)
        
        # Mock AI response for regeneration
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Regenerated content"))],
            usage=AsyncMock(total_tokens=150)
        )
        
        result = await orchestrator.generate_content_for_trend(
            trend_id,
            force_regenerate=True
        )
        
        assert result["success"] is True
        assert result["action"] == "regenerated"
        
        # Verify content was updated
        updated_content = await content_repo.get_by_id(content_id)
        assert "Regenerated" in updated_content["body"]
    
    @pytest.mark.asyncio
    async def test_generation_statistics(self, orchestrator):
        """Test generation statistics tracking."""
        stats = await orchestrator.get_generation_stats()
        
        assert "content_generated" in stats
        assert "sites_built" in stats
        assert "error_count" in stats
        assert "avg_generation_time" in stats
        assert "success_rate" in stats
