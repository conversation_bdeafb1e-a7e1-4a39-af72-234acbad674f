"""
Unit tests for TrendRepository and trend-related database operations.

Tests CRUD operations, filtering, pagination, and business logic
for trend data management.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from database.models.trend_model import TrendRepository


@pytest.mark.unit
@pytest.mark.asyncio
class TestTrendRepository:
    """Test suite for TrendRepository."""
    
    async def test_create_trend(self, trend_repo: TrendRepository, sample_trend_data):
        """Test creating a new trend."""
        trend_id = await trend_repo.create(sample_trend_data)
        
        assert trend_id is not None
        assert isinstance(trend_id, str)
        
        # Verify trend was created
        created_trend = await trend_repo.get_by_id(trend_id)
        assert created_trend is not None
        assert created_trend["keyword"] == sample_trend_data["keyword"]
        assert created_trend["slug"] == sample_trend_data["slug"]
        assert created_trend["category"] == sample_trend_data["category"]
    
    async def test_get_trend_by_id(self, trend_repo: TrendRepository, sample_trend_data):
        """Test retrieving trend by ID."""
        trend_id = await trend_repo.create(sample_trend_data)
        
        trend = await trend_repo.get_by_id(trend_id)
        
        assert trend is not None
        assert trend["id"] == trend_id
        assert trend["keyword"] == sample_trend_data["keyword"]
    
    async def test_get_nonexistent_trend(self, trend_repo: TrendRepository):
        """Test retrieving non-existent trend returns None."""
        fake_id = str(uuid.uuid4())
        trend = await trend_repo.get_by_id(fake_id)
        
        assert trend is None
    
    async def test_get_trend_by_slug(self, trend_repo: TrendRepository, sample_trend_data):
        """Test retrieving trend by slug."""
        await trend_repo.create(sample_trend_data)
        
        trend = await trend_repo.get_by_slug(sample_trend_data["slug"])
        
        assert trend is not None
        assert trend["slug"] == sample_trend_data["slug"]
        assert trend["keyword"] == sample_trend_data["keyword"]
    
    async def test_update_trend(self, trend_repo: TrendRepository, sample_trend_data):
        """Test updating trend data."""
        trend_id = await trend_repo.create(sample_trend_data)
        
        update_data = {
            "status": "approved",
            "score": 0.9,
            "search_volume": 2000
        }
        
        success = await trend_repo.update(trend_id, update_data)
        assert success is True
        
        # Verify updates
        updated_trend = await trend_repo.get_by_id(trend_id)
        assert updated_trend["status"] == "approved"
        assert updated_trend["score"] == 0.9
        assert updated_trend["search_volume"] == 2000
        assert updated_trend["keyword"] == sample_trend_data["keyword"]  # Unchanged
    
    async def test_delete_trend(self, trend_repo: TrendRepository, sample_trend_data):
        """Test deleting a trend."""
        trend_id = await trend_repo.create(sample_trend_data)
        
        # Verify trend exists
        trend = await trend_repo.get_by_id(trend_id)
        assert trend is not None
        
        # Delete trend
        success = await trend_repo.delete(trend_id)
        assert success is True
        
        # Verify trend is deleted
        deleted_trend = await trend_repo.get_by_id(trend_id)
        assert deleted_trend is None
    
    async def test_list_trends_with_pagination(self, trend_repo: TrendRepository):
        """Test listing trends with pagination."""
        # Create multiple trends
        trends_data = []
        for i in range(5):
            trend_data = {
                "keyword": f"Test Trend {i}",
                "slug": f"test-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "test_source",
                "search_volume": 1000 + i * 100,
                "score": 0.5 + i * 0.1
            }
            trend_id = await trend_repo.create(trend_data)
            trends_data.append((trend_id, trend_data))
        
        # Test pagination
        result = await trend_repo.list_with_pagination(page=1, page_size=3)
        
        assert "data" in result
        assert "pagination" in result
        assert len(result["data"]) == 3
        assert result["pagination"]["page"] == 1
        assert result["pagination"]["page_size"] == 3
        assert result["pagination"]["total_count"] == 5
        assert result["pagination"]["total_pages"] == 2
    
    async def test_list_trends_with_filters(self, trend_repo: TrendRepository):
        """Test listing trends with filters."""
        # Create trends with different categories
        tech_trend = {
            "keyword": "AI Technology",
            "slug": "ai-technology",
            "category": "Technology",
            "region": "US",
            "source": "test_source",
            "status": "approved"
        }
        
        health_trend = {
            "keyword": "Health Trend",
            "slug": "health-trend",
            "category": "Health",
            "region": "US",
            "source": "test_source",
            "status": "pending"
        }
        
        await trend_repo.create(tech_trend)
        await trend_repo.create(health_trend)
        
        # Filter by category
        tech_results = await trend_repo.list_with_pagination(
            filters={"category": "Technology"}
        )
        assert len(tech_results["data"]) == 1
        assert tech_results["data"][0]["category"] == "Technology"
        
        # Filter by status
        approved_results = await trend_repo.list_with_pagination(
            filters={"status": "approved"}
        )
        assert len(approved_results["data"]) == 1
        assert approved_results["data"][0]["status"] == "approved"
    
    async def test_search_trends(self, trend_repo: TrendRepository):
        """Test searching trends by keyword."""
        # Create trends with searchable keywords
        trends = [
            {"keyword": "Artificial Intelligence", "slug": "ai", "category": "Technology", "region": "US", "source": "test"},
            {"keyword": "Machine Learning", "slug": "ml", "category": "Technology", "region": "US", "source": "test"},
            {"keyword": "Healthy Eating", "slug": "healthy-eating", "category": "Health", "region": "US", "source": "test"}
        ]
        
        for trend in trends:
            await trend_repo.create(trend)
        
        # Search for AI-related trends
        ai_results = await trend_repo.search_trends("artificial")
        assert len(ai_results) >= 1
        assert any("artificial" in result["keyword"].lower() for result in ai_results)
        
        # Search for learning-related trends
        learning_results = await trend_repo.search_trends("learning")
        assert len(learning_results) >= 1
        assert any("learning" in result["keyword"].lower() for result in learning_results)
    
    async def test_get_trending_by_category(self, trend_repo: TrendRepository):
        """Test getting trending topics by category."""
        # Create trends with different scores
        trends = [
            {"keyword": "High Score Tech", "slug": "high-tech", "category": "Technology", "region": "US", "source": "test", "score": 0.9},
            {"keyword": "Medium Score Tech", "slug": "med-tech", "category": "Technology", "region": "US", "source": "test", "score": 0.6},
            {"keyword": "Low Score Tech", "slug": "low-tech", "category": "Technology", "region": "US", "source": "test", "score": 0.3},
            {"keyword": "High Score Health", "slug": "high-health", "category": "Health", "region": "US", "source": "test", "score": 0.8}
        ]
        
        for trend in trends:
            await trend_repo.create(trend)
        
        # Get top technology trends
        tech_trends = await trend_repo.get_trending_by_category("Technology", limit=2)
        assert len(tech_trends) == 2
        assert tech_trends[0]["score"] >= tech_trends[1]["score"]  # Ordered by score
        assert all(trend["category"] == "Technology" for trend in tech_trends)
    
    async def test_update_trend_scores(self, trend_repo: TrendRepository):
        """Test batch updating trend scores."""
        # Create trends
        trend_ids = []
        for i in range(3):
            trend_data = {
                "keyword": f"Trend {i}",
                "slug": f"trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "test_source",
                "score": 0.5
            }
            trend_id = await trend_repo.create(trend_data)
            trend_ids.append(trend_id)
        
        # Update scores
        score_updates = [
            {"id": trend_ids[0], "score": 0.8},
            {"id": trend_ids[1], "score": 0.9},
            {"id": trend_ids[2], "score": 0.7}
        ]
        
        updated_count = await trend_repo.batch_update_scores(score_updates)
        assert updated_count == 3
        
        # Verify updates
        for i, trend_id in enumerate(trend_ids):
            trend = await trend_repo.get_by_id(trend_id)
            expected_score = score_updates[i]["score"]
            assert trend["score"] == expected_score
    
    async def test_get_trends_for_processing(self, trend_repo: TrendRepository):
        """Test getting trends ready for processing."""
        # Create trends with different statuses
        trends = [
            {"keyword": "Pending Trend", "slug": "pending", "category": "Technology", "region": "US", "source": "test", "status": "pending"},
            {"keyword": "Approved Trend", "slug": "approved", "category": "Technology", "region": "US", "source": "test", "status": "approved"},
            {"keyword": "Rejected Trend", "slug": "rejected", "category": "Technology", "region": "US", "source": "test", "status": "rejected"}
        ]
        
        for trend in trends:
            await trend_repo.create(trend)
        
        # Get pending trends
        pending_trends = await trend_repo.get_trends_for_processing("pending", limit=10)
        assert len(pending_trends) == 1
        assert pending_trends[0]["status"] == "pending"
        
        # Get approved trends
        approved_trends = await trend_repo.get_trends_for_processing("approved", limit=10)
        assert len(approved_trends) == 1
        assert approved_trends[0]["status"] == "approved"
    
    async def test_get_trend_statistics(self, trend_repo: TrendRepository):
        """Test getting trend statistics."""
        # Create trends with various properties
        trends = [
            {"keyword": "Tech 1", "slug": "tech-1", "category": "Technology", "region": "US", "source": "source1", "status": "approved"},
            {"keyword": "Tech 2", "slug": "tech-2", "category": "Technology", "region": "US", "source": "source2", "status": "pending"},
            {"keyword": "Health 1", "slug": "health-1", "category": "Health", "region": "US", "source": "source1", "status": "approved"},
            {"keyword": "Health 2", "slug": "health-2", "category": "Health", "region": "UK", "source": "source2", "status": "rejected"}
        ]
        
        for trend in trends:
            await trend_repo.create(trend)
        
        stats = await trend_repo.get_trend_statistics()
        
        assert "total_trends" in stats
        assert "trends_by_category" in stats
        assert "trends_by_status" in stats
        assert "trends_by_region" in stats
        assert "trends_by_source" in stats
        
        assert stats["total_trends"] == 4
        assert stats["trends_by_category"]["Technology"] == 2
        assert stats["trends_by_category"]["Health"] == 2
        assert stats["trends_by_status"]["approved"] == 2
        assert stats["trends_by_status"]["pending"] == 1
        assert stats["trends_by_status"]["rejected"] == 1
    
    async def test_duplicate_slug_handling(self, trend_repo: TrendRepository, sample_trend_data):
        """Test handling of duplicate slugs."""
        # Create first trend
        await trend_repo.create(sample_trend_data)
        
        # Try to create trend with same slug
        duplicate_data = sample_trend_data.copy()
        duplicate_data["keyword"] = "Different Keyword"
        
        with pytest.raises(Exception):  # Should raise constraint violation
            await trend_repo.create(duplicate_data)
    
    async def test_trend_timestamps(self, trend_repo: TrendRepository, sample_trend_data):
        """Test that timestamps are properly set."""
        before_create = datetime.utcnow()
        trend_id = await trend_repo.create(sample_trend_data)
        after_create = datetime.utcnow()
        
        trend = await trend_repo.get_by_id(trend_id)
        
        # Check created_at timestamp
        created_at = trend["created_at"]
        assert before_create <= created_at <= after_create
        
        # Check updated_at timestamp
        updated_at = trend["updated_at"]
        assert before_create <= updated_at <= after_create
        
        # Update trend and check updated_at changes
        await trend_repo.update(trend_id, {"score": 0.9})
        updated_trend = await trend_repo.get_by_id(trend_id)
        
        assert updated_trend["updated_at"] > created_at
        assert updated_trend["created_at"] == created_at  # Should not change
