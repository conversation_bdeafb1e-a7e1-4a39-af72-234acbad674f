# Trend Platform Testing Guide

This document provides comprehensive information about testing the Trend Platform, including test structure, execution, and best practices.

## 🧪 Testing Framework

### Test Structure

The testing framework is organized into several categories:

```
tests/
├── conftest.py                 # Shared fixtures and configuration
├── test_database/             # Database layer tests
│   ├── test_trend_model.py
│   ├── test_content_model.py
│   └── test_deployment_model.py
├── test_api/                  # API endpoint tests
│   ├── test_trends_endpoints.py
│   ├── test_content_endpoints.py
│   └── test_deployment_endpoints.py
├── test_scraper/              # Scraping functionality tests
│   ├── test_trend_scraper.py
│   ├── test_data_processor.py
│   └── test_orchestrator.py
├── test_generator/            # Content generation tests
│   ├── test_content_generation.py
│   ├── test_ai_services.py
│   └── test_templates.py
├── test_deployment/           # Deployment system tests
│   ├── test_deployment_orchestrator.py
│   ├── test_coolify_client.py
│   └── test_domain_manager.py
├── test_e2e/                  # End-to-end tests
│   └── test_complete_workflow.py
└── test_performance/          # Performance and load tests
    └── test_load_testing.py
```

### Test Categories

Tests are organized using pytest markers:

- **`@pytest.mark.unit`**: Unit tests for individual components
- **`@pytest.mark.integration`**: Integration tests for component interactions
- **`@pytest.mark.e2e`**: End-to-end tests for complete workflows
- **`@pytest.mark.slow`**: Tests that take longer than 5 seconds
- **`@pytest.mark.external`**: Tests requiring external services
- **`@pytest.mark.performance`**: Performance and load tests

## 🚀 Running Tests

### Quick Start

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run all fast tests
python run_tests.py --fast

# Run all tests with coverage
python run_tests.py --all --coverage
```

### Test Runner Commands

The `run_tests.py` script provides convenient commands:

```bash
# Setup test environment
python run_tests.py --setup

# Run specific test categories
python run_tests.py --unit          # Unit tests only
python run_tests.py --integration   # Integration tests only
python run_tests.py --e2e           # End-to-end tests only
python run_tests.py --api           # API tests only
python run_tests.py --database      # Database tests only
python run_tests.py --scraper       # Scraper tests only
python run_tests.py --generator     # Generator tests only
python run_tests.py --deployment    # Deployment tests only

# Run specific test file
python run_tests.py --test tests/test_api/test_trends_endpoints.py

# Run with verbose output
python run_tests.py --unit --verbose

# Generate coverage report
python run_tests.py --all --coverage

# Code quality checks
python run_tests.py --lint          # Run linting
python run_tests.py --format        # Format code
python run_tests.py --security      # Security checks
```

### Direct Pytest Commands

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m "not slow"

# Run specific test file
pytest tests/test_api/test_trends_endpoints.py

# Run specific test function
pytest tests/test_api/test_trends_endpoints.py::TestTrendsAPI::test_get_trends_list

# Run with coverage
pytest --cov=. --cov-report=html

# Run with verbose output
pytest -v

# Run tests in parallel
pytest -n auto
```

## 🔧 Test Configuration

### Environment Setup

Tests use a separate test database and configuration:

```bash
# Test environment variables
export DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/test_trend_platform"
export REDIS_URL="redis://localhost:6379/1"
export OPENAI_API_KEY="test-key"
export ENVIRONMENT="testing"
```

### Test Database

The test suite automatically sets up a test database with:
- Isolated test data
- Automatic cleanup between tests
- Mock external services
- Fast in-memory operations where possible

### Fixtures

Common fixtures available in all tests:

```python
# Database fixtures
async def test_example(trend_repo, content_repo, deployment_repo):
    # Use repository instances with test database

# API fixtures
def test_api_example(client, async_client):
    # Use FastAPI test clients

# Mock fixtures
def test_with_mocks(mock_openai_client, mock_coolify_client):
    # Use mocked external services

# Data fixtures
def test_with_data(sample_trend_data, sample_content_data):
    # Use predefined test data
```

## 📊 Test Coverage

### Coverage Requirements

- **Minimum Coverage**: 80%
- **Critical Components**: 90%+ coverage required
- **API Endpoints**: 95%+ coverage required

### Coverage Reports

```bash
# Generate HTML coverage report
python run_tests.py --coverage

# View coverage report
open htmlcov/index.html

# Generate XML coverage report (for CI/CD)
coverage xml
```

### Coverage Exclusions

The following are excluded from coverage:
- Test files
- Migration scripts
- Development utilities
- Abstract base classes
- Error handling for impossible conditions

## 🧪 Writing Tests

### Test Structure

Follow this structure for new tests:

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.unit
class TestComponentName:
    """Test suite for ComponentName."""
    
    @pytest.fixture
    def component(self):
        """Create component instance for testing."""
        return ComponentName(config={})
    
    async def test_method_success(self, component):
        """Test successful method execution."""
        result = await component.method()
        assert result is not None
    
    async def test_method_error_handling(self, component):
        """Test method error handling."""
        with pytest.raises(ExpectedException):
            await component.method_with_error()
```

### Best Practices

1. **Test Naming**: Use descriptive names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Mock External Services**: Always mock external API calls
4. **Test Edge Cases**: Include error conditions and boundary cases
5. **Use Fixtures**: Leverage fixtures for common setup
6. **Async Testing**: Use `@pytest.mark.asyncio` for async tests

### Mock Guidelines

```python
# Mock external HTTP calls
@patch('aiohttp.ClientSession.get')
async def test_external_api(mock_get):
    mock_response = AsyncMock()
    mock_response.json.return_value = {"data": "test"}
    mock_get.return_value.__aenter__.return_value = mock_response

# Mock database operations
@patch('database.models.trend_model.TrendRepository.create')
async def test_database_operation(mock_create):
    mock_create.return_value = "test-id"

# Mock Celery tasks
@patch('scraper.tasks.scrape_trends_task.delay')
def test_celery_task(mock_task):
    mock_task.return_value = MagicMock(id="task-id")
```

## 🚀 Performance Testing

### Load Testing

Performance tests validate system behavior under load:

```python
@pytest.mark.slow
@pytest.mark.performance
async def test_api_performance(async_client):
    """Test API performance under load."""
    # Run concurrent requests
    tasks = [async_client.get("/api/v1/trends") for _ in range(20)]
    results = await asyncio.gather(*tasks)
    
    # Assert performance requirements
    assert all(r.status_code == 200 for r in results)
```

### Performance Benchmarks

- **API Response Time**: < 500ms average
- **Database Queries**: < 100ms average
- **Content Generation**: < 30s for batch operations
- **Deployment**: < 10 minutes end-to-end

## 🔄 Continuous Integration

### GitHub Actions

```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: Install dependencies
        run: pip install -r requirements-test.txt
      - name: Run tests
        run: python run_tests.py --all --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### Pre-commit Hooks

```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: tests
        name: Run tests
        entry: python run_tests.py --fast
        language: system
        pass_filenames: false
```

## 🐛 Debugging Tests

### Common Issues

1. **Database Connection**: Ensure test database is running
2. **Async Tests**: Use `@pytest.mark.asyncio` decorator
3. **Mock Conflicts**: Clear mocks between tests
4. **External Services**: Mock all external API calls

### Debug Commands

```bash
# Run tests with debug output
pytest -v -s

# Run single test with debugging
pytest -v -s tests/test_api/test_trends_endpoints.py::test_specific_function

# Debug with pdb
pytest --pdb

# Show test durations
pytest --durations=10
```

### Test Data Inspection

```python
# Add debugging to tests
def test_with_debug(trend_repo):
    trend_id = await trend_repo.create(sample_data)
    
    # Debug: Print created data
    trend = await trend_repo.get_by_id(trend_id)
    print(f"Created trend: {trend}")
    
    assert trend is not None
```

## 📈 Test Metrics

### Key Metrics

- **Test Count**: 150+ tests across all categories
- **Coverage**: 85%+ overall coverage
- **Performance**: All tests complete within 5 minutes
- **Reliability**: 99%+ test pass rate

### Monitoring

```bash
# Test execution time
pytest --durations=0

# Memory usage during tests
pytest --memray

# Test flakiness detection
pytest --flake-finder
```

## 🔧 Maintenance

### Regular Tasks

1. **Weekly**: Review test coverage and add missing tests
2. **Monthly**: Update test dependencies and fixtures
3. **Quarterly**: Performance benchmark review
4. **Release**: Full test suite execution and validation

### Test Data Management

```python
# Clean test data
@pytest.fixture(autouse=True)
async def cleanup_test_data(db_pool):
    yield
    # Cleanup after each test
    async with db_pool.acquire() as conn:
        await conn.execute("TRUNCATE trends, content, deployments CASCADE")
```

## 📚 Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [FastAPI Testing](https://fastapi.tiangolo.com/tutorial/testing/)
- [Async Testing Guide](https://pytest-asyncio.readthedocs.io/)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)

## 🆘 Support

For testing issues:
1. Check this documentation
2. Review test logs and error messages
3. Verify test environment setup
4. Contact development team
