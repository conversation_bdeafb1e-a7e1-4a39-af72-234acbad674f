"""
Unit tests for trend scraping functionality.

Tests scraper components, data processing, filtering, and validation
for the trend discovery system.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import aiohttp

from scraper.base_scraper import Base<PERSON><PERSON>raper, ScrapedTrend
from scraper.google_trends_scraper import GoogleTrendsScraper
from scraper.data_processor import DataProcessor, TrendFilter
from scraper.utils.rate_limiter import RateLimiter


@pytest.mark.unit
class TestBaseScraper:
    """Test suite for BaseScraper abstract class."""
    
    def test_scraped_trend_creation(self):
        """Test ScrapedTrend data class creation."""
        trend = ScrapedTrend(
            keyword="Test Trend",
            category="Technology",
            region="US",
            source="test_scraper",
            search_volume=1000,
            growth_rate=0.15,
            raw_data={"test": "data"}
        )
        
        assert trend.keyword == "Test Trend"
        assert trend.category == "Technology"
        assert trend.region == "US"
        assert trend.source == "test_scraper"
        assert trend.search_volume == 1000
        assert trend.growth_rate == 0.15
        assert trend.raw_data == {"test": "data"}
    
    def test_scraped_trend_slug_generation(self):
        """Test automatic slug generation."""
        trend = ScrapedTrend(
            keyword="Test Trend With Spaces",
            category="Technology",
            region="US",
            source="test"
        )
        
        assert trend.slug == "test-trend-with-spaces"
    
    def test_scraped_trend_validation(self):
        """Test ScrapedTrend validation."""
        # Valid trend
        valid_trend = ScrapedTrend(
            keyword="Valid Trend",
            category="Technology",
            region="US",
            source="test"
        )
        assert valid_trend.is_valid()
        
        # Invalid trend - empty keyword
        invalid_trend = ScrapedTrend(
            keyword="",
            category="Technology",
            region="US",
            source="test"
        )
        assert not invalid_trend.is_valid()
    
    def test_base_scraper_abstract_methods(self):
        """Test that BaseScraper cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseScraper()


@pytest.mark.unit
class TestGoogleTrendsScraper:
    """Test suite for GoogleTrendsScraper."""
    
    @pytest.fixture
    def scraper(self):
        """Create GoogleTrendsScraper instance for testing."""
        config = {
            "regions": ["US", "UK"],
            "categories": ["Technology", "Health"],
            "max_trends_per_region": 10
        }
        return GoogleTrendsScraper(config)
    
    @pytest.mark.asyncio
    async def test_scraper_initialization(self, scraper):
        """Test scraper initialization."""
        assert scraper.name == "google_trends"
        assert "US" in scraper.regions
        assert "UK" in scraper.regions
        assert "Technology" in scraper.categories
    
    @pytest.mark.asyncio
    async def test_fetch_rss_feed(self, scraper):
        """Test RSS feed fetching."""
        mock_response = """<?xml version="1.0" encoding="UTF-8"?>
        <rss version="2.0">
            <channel>
                <item>
                    <title>Test Trend</title>
                    <description>Test trend description</description>
                    <pubDate>Mon, 01 Jan 2024 12:00:00 GMT</pubDate>
                </item>
            </channel>
        </rss>"""
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response_obj = AsyncMock()
            mock_response_obj.text.return_value = mock_response
            mock_response_obj.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response_obj
            
            feed_data = await scraper._fetch_rss_feed("https://test-url.com")
            
            assert feed_data is not None
            assert "Test Trend" in str(feed_data)
    
    @pytest.mark.asyncio
    async def test_parse_rss_trends(self, scraper):
        """Test parsing trends from RSS feed."""
        mock_feed = MagicMock()
        mock_feed.entries = [
            MagicMock(
                title="Artificial Intelligence",
                summary="AI trend description",
                published_parsed=datetime.now().timetuple()
            ),
            MagicMock(
                title="Machine Learning",
                summary="ML trend description", 
                published_parsed=datetime.now().timetuple()
            )
        ]
        
        trends = await scraper._parse_rss_trends(mock_feed, "Technology", "US")
        
        assert len(trends) == 2
        assert trends[0].keyword == "Artificial Intelligence"
        assert trends[0].category == "Technology"
        assert trends[0].region == "US"
        assert trends[0].source == "google_trends"
    
    @pytest.mark.asyncio
    async def test_scrape_region_category(self, scraper):
        """Test scraping specific region and category."""
        with patch.object(scraper, '_fetch_rss_feed') as mock_fetch:
            with patch.object(scraper, '_parse_rss_trends') as mock_parse:
                mock_fetch.return_value = MagicMock()
                mock_parse.return_value = [
                    ScrapedTrend(
                        keyword="Test Trend",
                        category="Technology",
                        region="US",
                        source="google_trends"
                    )
                ]
                
                trends = await scraper._scrape_region_category("US", "Technology")
                
                assert len(trends) == 1
                assert trends[0].keyword == "Test Trend"
                mock_fetch.assert_called_once()
                mock_parse.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_scrape_all_trends(self, scraper):
        """Test scraping all configured regions and categories."""
        with patch.object(scraper, '_scrape_region_category') as mock_scrape:
            mock_scrape.return_value = [
                ScrapedTrend(
                    keyword="Test Trend",
                    category="Technology",
                    region="US",
                    source="google_trends"
                )
            ]
            
            all_trends = await scraper.scrape()
            
            # Should call scrape for each region-category combination
            expected_calls = len(scraper.regions) * len(scraper.categories)
            assert mock_scrape.call_count == expected_calls
            assert len(all_trends) == expected_calls
    
    @pytest.mark.asyncio
    async def test_error_handling(self, scraper):
        """Test error handling during scraping."""
        with patch.object(scraper, '_fetch_rss_feed') as mock_fetch:
            mock_fetch.side_effect = aiohttp.ClientError("Network error")
            
            trends = await scraper._scrape_region_category("US", "Technology")
            
            # Should return empty list on error
            assert trends == []


@pytest.mark.unit
class TestDataProcessor:
    """Test suite for DataProcessor."""
    
    @pytest.fixture
    def processor(self):
        """Create DataProcessor instance for testing."""
        config = {
            "min_search_volume": 100,
            "min_growth_rate": 0.1,
            "blocked_keywords": ["spam", "inappropriate"],
            "quality_threshold": 0.5
        }
        return DataProcessor(config)
    
    def test_processor_initialization(self, processor):
        """Test processor initialization."""
        assert processor.min_search_volume == 100
        assert processor.min_growth_rate == 0.1
        assert "spam" in processor.blocked_keywords
    
    def test_validate_trend_valid(self, processor):
        """Test validation of valid trend."""
        trend = ScrapedTrend(
            keyword="Valid Trend",
            category="Technology",
            region="US",
            source="test",
            search_volume=1000,
            growth_rate=0.15
        )
        
        assert processor.validate_trend(trend) is True
    
    def test_validate_trend_low_search_volume(self, processor):
        """Test validation fails for low search volume."""
        trend = ScrapedTrend(
            keyword="Low Volume Trend",
            category="Technology",
            region="US",
            source="test",
            search_volume=50,  # Below threshold
            growth_rate=0.15
        )
        
        assert processor.validate_trend(trend) is False
    
    def test_validate_trend_blocked_keyword(self, processor):
        """Test validation fails for blocked keywords."""
        trend = ScrapedTrend(
            keyword="spam trend",  # Contains blocked keyword
            category="Technology",
            region="US",
            source="test",
            search_volume=1000,
            growth_rate=0.15
        )
        
        assert processor.validate_trend(trend) is False
    
    def test_calculate_trend_score(self, processor):
        """Test trend score calculation."""
        trend = ScrapedTrend(
            keyword="High Quality Trend",
            category="Technology",
            region="US",
            source="test",
            search_volume=5000,
            growth_rate=0.25
        )
        
        score = processor.calculate_trend_score(trend)
        
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should be high quality
    
    def test_deduplicate_trends(self, processor):
        """Test trend deduplication."""
        trends = [
            ScrapedTrend(keyword="Duplicate Trend", category="Tech", region="US", source="test1"),
            ScrapedTrend(keyword="Duplicate Trend", category="Tech", region="US", source="test2"),
            ScrapedTrend(keyword="Unique Trend", category="Tech", region="US", source="test1")
        ]
        
        deduplicated = processor.deduplicate_trends(trends)
        
        assert len(deduplicated) == 2
        keywords = [t.keyword for t in deduplicated]
        assert "Duplicate Trend" in keywords
        assert "Unique Trend" in keywords
    
    def test_process_trends_pipeline(self, processor):
        """Test complete trend processing pipeline."""
        raw_trends = [
            ScrapedTrend(keyword="Good Trend", category="Tech", region="US", source="test", search_volume=1000, growth_rate=0.15),
            ScrapedTrend(keyword="spam trend", category="Tech", region="US", source="test", search_volume=1000, growth_rate=0.15),
            ScrapedTrend(keyword="Low Volume", category="Tech", region="US", source="test", search_volume=50, growth_rate=0.15),
            ScrapedTrend(keyword="Good Trend", category="Tech", region="US", source="test2", search_volume=1000, growth_rate=0.15)  # Duplicate
        ]
        
        processed = processor.process_trends(raw_trends)
        
        # Should filter out spam, low volume, and deduplicate
        assert len(processed) == 1
        assert processed[0].keyword == "Good Trend"
        assert hasattr(processed[0], 'score')
        assert processed[0].score > 0


@pytest.mark.unit
class TestTrendFilter:
    """Test suite for TrendFilter."""
    
    def test_keyword_filter(self):
        """Test keyword-based filtering."""
        filter_config = {
            "blocked_keywords": ["spam", "inappropriate"],
            "required_keywords": ["technology", "innovation"]
        }
        trend_filter = TrendFilter(filter_config)
        
        # Should block spam
        spam_trend = ScrapedTrend(keyword="spam content", category="Tech", region="US", source="test")
        assert not trend_filter.passes_keyword_filter(spam_trend)
        
        # Should pass clean keyword
        clean_trend = ScrapedTrend(keyword="technology innovation", category="Tech", region="US", source="test")
        assert trend_filter.passes_keyword_filter(clean_trend)
    
    def test_category_filter(self):
        """Test category-based filtering."""
        filter_config = {
            "allowed_categories": ["Technology", "Health"],
            "blocked_categories": ["Adult", "Gambling"]
        }
        trend_filter = TrendFilter(filter_config)
        
        # Should allow Technology
        tech_trend = ScrapedTrend(keyword="AI Trend", category="Technology", region="US", source="test")
        assert trend_filter.passes_category_filter(tech_trend)
        
        # Should block Adult
        adult_trend = ScrapedTrend(keyword="Some Trend", category="Adult", region="US", source="test")
        assert not trend_filter.passes_category_filter(adult_trend)
    
    def test_region_filter(self):
        """Test region-based filtering."""
        filter_config = {
            "allowed_regions": ["US", "UK", "CA"]
        }
        trend_filter = TrendFilter(filter_config)
        
        # Should allow US
        us_trend = ScrapedTrend(keyword="US Trend", category="Tech", region="US", source="test")
        assert trend_filter.passes_region_filter(us_trend)
        
        # Should block other regions
        other_trend = ScrapedTrend(keyword="Other Trend", category="Tech", region="FR", source="test")
        assert not trend_filter.passes_region_filter(other_trend)
    
    def test_quality_filter(self):
        """Test quality-based filtering."""
        filter_config = {
            "min_search_volume": 1000,
            "min_growth_rate": 0.1,
            "min_keyword_length": 5
        }
        trend_filter = TrendFilter(filter_config)
        
        # Should pass high quality
        quality_trend = ScrapedTrend(
            keyword="High Quality Trend",
            category="Tech",
            region="US",
            source="test",
            search_volume=5000,
            growth_rate=0.25
        )
        assert trend_filter.passes_quality_filter(quality_trend)
        
        # Should fail low quality
        low_quality_trend = ScrapedTrend(
            keyword="Low",  # Too short
            category="Tech",
            region="US",
            source="test",
            search_volume=100,  # Too low
            growth_rate=0.05   # Too low
        )
        assert not trend_filter.passes_quality_filter(low_quality_trend)


@pytest.mark.unit
class TestRateLimiter:
    """Test suite for RateLimiter."""
    
    @pytest.mark.asyncio
    async def test_rate_limiter_allows_requests(self):
        """Test rate limiter allows requests within limits."""
        limiter = RateLimiter(requests_per_second=2, burst_size=5)
        
        # Should allow initial requests
        for _ in range(3):
            allowed = await limiter.acquire()
            assert allowed is True
    
    @pytest.mark.asyncio
    async def test_rate_limiter_blocks_excess_requests(self):
        """Test rate limiter blocks excess requests."""
        limiter = RateLimiter(requests_per_second=1, burst_size=2)
        
        # Use up burst allowance
        for _ in range(2):
            allowed = await limiter.acquire()
            assert allowed is True
        
        # Next request should be limited
        allowed = await limiter.acquire()
        assert allowed is False
    
    @pytest.mark.asyncio
    async def test_rate_limiter_recovery(self):
        """Test rate limiter recovers over time."""
        limiter = RateLimiter(requests_per_second=10, burst_size=1)
        
        # Use up allowance
        allowed = await limiter.acquire()
        assert allowed is True
        
        # Should be blocked immediately
        allowed = await limiter.acquire()
        assert allowed is False
        
        # Wait for recovery (in real test, would use time mocking)
        import asyncio
        await asyncio.sleep(0.2)  # Allow some recovery
        
        allowed = await limiter.acquire()
        # May or may not be allowed depending on timing
