[tool:pytest]
# Pytest configuration for Trend Platform

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    slow: Tests that take longer than 5 seconds
    external: Tests that require external services
    database: Tests that require database connection
    api: Tests for API endpoints
    scraper: Tests for scraping functionality
    generator: Tests for content generation
    deployment: Tests for deployment functionality

# Test timeout
timeout = 300

# Async support
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:aiohttp.*

# Coverage configuration
[coverage:run]
source = .
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */alembic/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
