"""
Unit tests for deployment orchestration functionality.

Tests deployment automation, Coolify integration, DNS management,
and deployment tracking for the trend platform.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from deployment.deployment_orchestrator import DeploymentOrchestrator
from deployment.coolify_client import CoolifyClient, CoolifyDeployment
from deployment.domain_manager import DomainManager
from deployment.git_operations import GitOperations


@pytest.mark.unit
class TestCoolifyClient:
    """Test suite for Coolify API client."""
    
    @pytest.fixture
    def coolify_client(self, mock_coolify_client):
        """Create Coolify client with mocked HTTP responses."""
        client = CoolifyClient(
            api_url="https://test-coolify.com",
            api_token="test-token"
        )
        # Replace the actual session with our mock
        client._get_session = AsyncMock(return_value=mock_coolify_client)
        return client
    
    @pytest.mark.asyncio
    async def test_create_application(self, coolify_client):
        """Test creating application in Coolify."""
        result = await coolify_client.create_application(
            name="test-app",
            git_repository="https://github.com/test/repo.git",
            git_branch="main",
            build_pack="static"
        )
        
        assert result["id"] == "test-app-id"
        assert result["name"] == "test-app"
        assert result["default_domain"] == "test-app.coolify.io"
    
    @pytest.mark.asyncio
    async def test_deploy_application(self, coolify_client):
        """Test deploying application."""
        deployment = await coolify_client.deploy_application(
            application_id="test-app-id",
            force_rebuild=True,
            commit_sha="abc123"
        )
        
        assert deployment.id == "test-deployment-id"
        assert deployment.application_id == "test-app-id"
        assert deployment.status == "pending"
        assert deployment.commit_sha == "abc123"
    
    @pytest.mark.asyncio
    async def test_get_deployment_status(self, coolify_client):
        """Test getting deployment status."""
        deployment = await coolify_client.get_deployment_status("test-deployment-id")
        
        assert deployment.id == "test-deployment-id"
        assert deployment.status == "success"
        assert deployment.deploy_url == "https://test-app.coolify.io"
    
    @pytest.mark.asyncio
    async def test_connection_test(self, coolify_client):
        """Test Coolify connection testing."""
        # Mock successful connection
        with patch.object(coolify_client, '_get_session') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"version": "1.0.0"}
            
            mock_session.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await coolify_client.test_connection()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_error_handling(self, coolify_client):
        """Test error handling for API failures."""
        with patch.object(coolify_client, '_get_session') as mock_session:
            mock_session.side_effect = Exception("Connection failed")
            
            with pytest.raises(Exception):
                await coolify_client.create_application("test", "repo", "main")


@pytest.mark.unit
class TestDomainManager:
    """Test suite for domain management."""
    
    @pytest.fixture
    def domain_manager(self, mock_cloudflare_client):
        """Create domain manager with mocked Cloudflare client."""
        config = {
            "cloudflare_api_token": "test-token",
            "zone_id": "test-zone",
            "base_domain": "trends.example.com"
        }
        manager = DomainManager(config)
        manager.cloudflare_client = mock_cloudflare_client
        return manager
    
    @pytest.mark.asyncio
    async def test_generate_domain_name(self, domain_manager):
        """Test domain name generation."""
        domain = await domain_manager.generate_domain_name("test-trend")
        assert domain == "test-trend.trends.example.com"
    
    @pytest.mark.asyncio
    async def test_create_cname_record(self, domain_manager, mock_cloudflare_client):
        """Test CNAME record creation."""
        result = await domain_manager.create_cname_record(
            domain="test.example.com",
            target="target.example.com"
        )
        
        assert result["success"] is True
        assert result["action"] == "created"
        mock_cloudflare_client.create_dns_record.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setup_trend_domain(self, domain_manager):
        """Test complete trend domain setup."""
        result = await domain_manager.setup_trend_domain(
            trend_slug="test-trend",
            target_domain="app.coolify.io",
            custom_domain="custom.example.com"
        )
        
        assert "primary_domain" in result
        assert "custom_domain" in result
        assert result["primary_domain"]["domain"] == "test-trend.trends.example.com"
        assert result["custom_domain"]["domain"] == "custom.example.com"
    
    @pytest.mark.asyncio
    async def test_domain_status_check(self, domain_manager, mock_cloudflare_client):
        """Test domain configuration status check."""
        status = await domain_manager.get_domain_status("test.example.com")
        
        assert status["configured"] is True
        assert status["record_type"] == "CNAME"
        assert status["content"] == "target.example.com"


@pytest.mark.unit
class TestGitOperations:
    """Test suite for Git operations."""
    
    @pytest.fixture
    def git_ops(self, temp_dir):
        """Create Git operations instance."""
        config = {
            "git_user": "Test User",
            "git_email": "<EMAIL>",
            "default_branch": "main"
        }
        return GitOperations(config)
    
    @pytest.mark.asyncio
    async def test_repository_info(self, git_ops, temp_dir):
        """Test getting repository information."""
        # Create a mock git repository
        (temp_dir / ".git").mkdir()
        
        with patch.object(git_ops, '_run_git_command') as mock_git:
            mock_git.side_effect = [
                MagicMock(stdout=b"main\n"),  # current branch
                MagicMock(stdout=b"abc123\n"),  # commit SHA
                MagicMock(stdout=b"https://github.com/test/repo.git\n"),  # remote URL
                MagicMock(returncode=1)  # has changes
            ]
            
            info = await git_ops.get_repository_info(str(temp_dir))
            
            assert info["is_git_repo"] is True
            assert info["current_branch"] == "main"
            assert info["commit_sha"] == "abc123"
            assert info["remote_url"] == "https://github.com/test/repo.git"
    
    @pytest.mark.asyncio
    async def test_create_deployment_repository(self, git_ops, temp_dir):
        """Test creating deployment repository."""
        site_path = temp_dir / "test-site"
        site_path.mkdir()
        
        # Create some test files
        (site_path / "index.html").write_text("<html>Test</html>")
        (site_path / "style.css").write_text("body { margin: 0; }")
        
        with patch.object(git_ops, '_run_git_command') as mock_git:
            mock_git.return_value = MagicMock(returncode=0)
            
            result = await git_ops.create_deployment_repository(
                site_path=str(site_path),
                repo_name="test-repo",
                remote_url="https://github.com/test/repo.git"
            )
            
            assert result["success"] is True
            assert result["repository"] == "test-repo"
            assert result["branch"] == "main"
    
    @pytest.mark.asyncio
    async def test_update_repository(self, git_ops, temp_dir):
        """Test updating existing repository."""
        repo_path = temp_dir / "test-repo"
        repo_path.mkdir()
        (repo_path / ".git").mkdir()
        
        with patch.object(git_ops, '_run_git_command') as mock_git:
            with patch.object(git_ops, '_has_uncommitted_changes') as mock_changes:
                mock_changes.return_value = True
                mock_git.return_value = MagicMock(returncode=0)
                
                result = await git_ops.update_repository(
                    site_path=str(repo_path),
                    commit_message="Update site"
                )
                
                assert result["success"] is True
                assert result["has_changes"] is True


@pytest.mark.integration
class TestDeploymentOrchestrator:
    """Integration tests for deployment orchestration."""
    
    @pytest.fixture
    def orchestrator(self, deployment_repo, trend_repo, content_repo):
        """Create deployment orchestrator with mocked dependencies."""
        config = {
            "coolify": {
                "api_url": "https://test-coolify.com",
                "api_token": "test-token"
            },
            "dns": {
                "cloudflare_api_token": "test-cf-token",
                "zone_id": "test-zone",
                "base_domain": "trends.example.com"
            },
            "git": {
                "git_user": "Test User",
                "git_email": "<EMAIL>"
            }
        }
        
        return DeploymentOrchestrator(
            deployment_repository=deployment_repo,
            trend_repository=trend_repo,
            content_repository=content_repo,
            config=config
        )
    
    @pytest.mark.asyncio
    async def test_deploy_trend_site(self, orchestrator, trend_repo, content_repo, deployment_repo, sample_trend_data, sample_content_data):
        """Test complete trend site deployment."""
        # Create test trend and content
        trend_id = await trend_repo.create(sample_trend_data)
        content_data = sample_content_data.copy()
        content_data["trend_id"] = trend_id
        content_id = await content_repo.create(content_data)
        
        # Mock all external dependencies
        with patch.object(orchestrator, '_prepare_git_repository') as mock_git:
            with patch.object(orchestrator, '_setup_coolify_application') as mock_coolify:
                with patch.object(orchestrator, '_deploy_to_coolify') as mock_deploy:
                    with patch.object(orchestrator.deployment_tracker, 'start_deployment_tracking') as mock_track:
                        
                        # Setup mocks
                        mock_git.return_value = {
                            "success": True,
                            "remote_url": "https://github.com/test/repo.git",
                            "commit_sha": "abc123",
                            "branch": "main"
                        }
                        
                        mock_coolify.return_value = {
                            "success": True,
                            "application_id": "test-app-id",
                            "default_domain": "test-app.coolify.io"
                        }
                        
                        mock_deploy.return_value = {
                            "success": True,
                            "deployment_id": "test-deployment-id"
                        }
                        
                        mock_track.return_value = True
                        
                        # Execute deployment
                        result = await orchestrator.deploy_trend_site(trend_id)
                        
                        assert result["success"] is True
                        assert result["deployment_id"] is not None
                        assert result["trend_id"] == trend_id
                        assert result["application_id"] == "test-app-id"
                        assert result["deploy_url"] == "test-app.coolify.io"
                        
                        # Verify deployment record was created
                        deployment = await deployment_repo.get_by_trend_id(trend_id)
                        assert deployment is not None
                        assert deployment["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_batch_deploy_trends(self, orchestrator, trend_repo, content_repo):
        """Test batch deployment of multiple trends."""
        # Create multiple trends with content
        trend_ids = []
        for i in range(3):
            trend_data = {
                "keyword": f"Test Trend {i}",
                "slug": f"test-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "test"
            }
            trend_id = await trend_repo.create(trend_data)
            
            content_data = {
                "trend_id": trend_id,
                "title": f"Article {i}",
                "description": f"Description {i}",
                "body": f"Content {i}",
                "word_count": 100
            }
            await content_repo.create(content_data)
            
            trend_ids.append(trend_id)
        
        # Mock deployment for each trend
        with patch.object(orchestrator, 'deploy_trend_site') as mock_deploy:
            mock_deploy.return_value = {
                "success": True,
                "deployment_id": "test-deployment-id",
                "trend_id": "test-trend-id"
            }
            
            result = await orchestrator.batch_deploy_trends(
                trend_ids=trend_ids,
                max_concurrent=2
            )
            
            assert result["total_trends"] == 3
            assert result["successful"] == 3
            assert result["failed"] == 0
            assert result["success_rate"] == 1.0
    
    @pytest.mark.asyncio
    async def test_deployment_status_tracking(self, orchestrator, deployment_repo):
        """Test deployment status tracking."""
        # Create a test deployment record
        deployment_data = {
            "trend_id": "test-trend-id",
            "status": "building",
            "coolify_deployment_uuid": "test-deployment-id"
        }
        deployment_id = await deployment_repo.create(deployment_data)
        
        # Mock Coolify status check
        with patch.object(orchestrator.coolify_client, 'get_deployment_status') as mock_status:
            mock_status.return_value = CoolifyDeployment(
                id="test-deployment-id",
                application_id="test-app-id",
                status="success",
                deploy_url="https://test-app.coolify.io"
            )
            
            status = await orchestrator.get_deployment_status(deployment_id)
            
            assert status["deployment"]["id"] == deployment_id
            assert status["coolify_status"]["status"] == "success"
            assert status["coolify_status"]["deploy_url"] == "https://test-app.coolify.io"
    
    @pytest.mark.asyncio
    async def test_deployment_error_handling(self, orchestrator, trend_repo, content_repo, sample_trend_data, sample_content_data):
        """Test deployment error handling."""
        # Create test trend and content
        trend_id = await trend_repo.create(sample_trend_data)
        content_data = sample_content_data.copy()
        content_data["trend_id"] = trend_id
        await content_repo.create(content_data)
        
        # Mock Git failure
        with patch.object(orchestrator, '_prepare_git_repository') as mock_git:
            mock_git.return_value = {
                "success": False,
                "error": "Git operation failed"
            }
            
            result = await orchestrator.deploy_trend_site(trend_id)
            
            assert result["success"] is False
            assert "Git preparation failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_deployment_metrics(self, orchestrator):
        """Test deployment metrics collection."""
        metrics = await orchestrator.get_deployment_metrics()
        
        assert "orchestrator_stats" in metrics
        assert "tracker_metrics" in metrics
        assert "timestamp" in metrics
        
        orchestrator_stats = metrics["orchestrator_stats"]
        assert "deployments_started" in orchestrator_stats
        assert "deployments_completed" in orchestrator_stats
        assert "deployments_failed" in orchestrator_stats
