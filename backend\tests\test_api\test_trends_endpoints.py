"""
Integration tests for trend API endpoints.

Tests REST API functionality, request/response handling, authentication,
and error cases for trend management endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
import json


@pytest.mark.integration
class TestTrendsAPI:
    """Test suite for trends API endpoints."""
    
    def test_get_trends_list(self, client: TestClient):
        """Test GET /api/v1/trends endpoint."""
        response = client.get("/api/v1/trends")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "data" in data
        assert "pagination" in data
        assert isinstance(data["data"], list)
        assert "page" in data["pagination"]
        assert "page_size" in data["pagination"]
        assert "total_count" in data["pagination"]
    
    def test_get_trends_with_pagination(self, client: TestClient):
        """Test trends list with pagination parameters."""
        response = client.get("/api/v1/trends?page=1&page_size=5")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["pagination"]["page"] == 1
        assert data["pagination"]["page_size"] == 5
    
    def test_get_trends_with_filters(self, client: TestClient):
        """Test trends list with filters."""
        response = client.get("/api/v1/trends?category=Technology&status=approved")
        
        assert response.status_code == 200
        data = response.json()
        
        # All returned trends should match filters
        for trend in data["data"]:
            if trend.get("category"):
                assert trend["category"] == "Technology"
            if trend.get("status"):
                assert trend["status"] == "approved"
    
    def test_get_trend_by_id(self, client: TestClient):
        """Test GET /api/v1/trends/{trend_id} endpoint."""
        # First create a trend to test with
        with patch('database.models.trend_model.TrendRepository.get_by_id') as mock_get:
            mock_get.return_value = {
                "id": "test-id",
                "keyword": "Test Trend",
                "slug": "test-trend",
                "category": "Technology",
                "region": "US",
                "source": "test_source",
                "status": "pending"
            }
            
            response = client.get("/api/v1/trends/test-id")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["id"] == "test-id"
            assert data["keyword"] == "Test Trend"
            assert data["category"] == "Technology"
    
    def test_get_nonexistent_trend(self, client: TestClient):
        """Test getting non-existent trend returns 404."""
        with patch('database.models.trend_model.TrendRepository.get_by_id') as mock_get:
            mock_get.return_value = None
            
            response = client.get("/api/v1/trends/nonexistent-id")
            
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
    
    @patch('api.v1.trends.get_current_user')
    def test_create_trend(self, mock_auth, client: TestClient):
        """Test POST /api/v1/trends endpoint."""
        mock_auth.return_value = {"id": "user-id", "username": "testuser"}
        
        trend_data = {
            "keyword": "New Test Trend",
            "category": "Technology",
            "region": "US",
            "source": "manual",
            "search_volume": 1000,
            "growth_rate": 0.15
        }
        
        with patch('database.models.trend_model.TrendRepository.create') as mock_create:
            mock_create.return_value = "new-trend-id"
            
            response = client.post(
                "/api/v1/trends",
                json=trend_data,
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 201
            data = response.json()
            
            assert data["success"] is True
            assert data["trend_id"] == "new-trend-id"
            assert "created successfully" in data["message"]
    
    def test_create_trend_without_auth(self, client: TestClient):
        """Test creating trend without authentication fails."""
        trend_data = {
            "keyword": "New Test Trend",
            "category": "Technology",
            "region": "US",
            "source": "manual"
        }
        
        response = client.post("/api/v1/trends", json=trend_data)
        
        assert response.status_code == 401
    
    def test_create_trend_invalid_data(self, client: TestClient):
        """Test creating trend with invalid data."""
        with patch('api.v1.trends.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": "user-id"}
            
            # Missing required fields
            invalid_data = {
                "keyword": "Test"
                # Missing category, region, source
            }
            
            response = client.post(
                "/api/v1/trends",
                json=invalid_data,
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 422  # Validation error
    
    @patch('api.v1.trends.get_current_user')
    def test_update_trend(self, mock_auth, client: TestClient):
        """Test PUT /api/v1/trends/{trend_id} endpoint."""
        mock_auth.return_value = {"id": "user-id", "username": "testuser"}
        
        update_data = {
            "status": "approved",
            "score": 0.9
        }
        
        with patch('database.models.trend_model.TrendRepository.update') as mock_update:
            mock_update.return_value = True
            
            response = client.put(
                "/api/v1/trends/test-id",
                json=update_data,
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "updated successfully" in data["message"]
    
    @patch('api.v1.trends.get_current_user')
    def test_delete_trend(self, mock_auth, client: TestClient):
        """Test DELETE /api/v1/trends/{trend_id} endpoint."""
        mock_auth.return_value = {"id": "user-id", "username": "testuser"}
        
        with patch('database.models.trend_model.TrendRepository.delete') as mock_delete:
            mock_delete.return_value = True
            
            response = client.delete(
                "/api/v1/trends/test-id",
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "deleted successfully" in data["message"]
    
    def test_search_trends(self, client: TestClient):
        """Test GET /api/v1/trends/search endpoint."""
        with patch('database.models.trend_model.TrendRepository.search_trends') as mock_search:
            mock_search.return_value = [
                {
                    "id": "trend-1",
                    "keyword": "Artificial Intelligence",
                    "category": "Technology",
                    "score": 0.9
                }
            ]
            
            response = client.get("/api/v1/trends/search?q=artificial")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "results" in data
            assert len(data["results"]) == 1
            assert "artificial" in data["results"][0]["keyword"].lower()
    
    def test_search_trends_empty_query(self, client: TestClient):
        """Test search with empty query."""
        response = client.get("/api/v1/trends/search?q=")
        
        assert response.status_code == 400
        assert "query parameter is required" in response.json()["detail"].lower()
    
    def test_get_trending_by_category(self, client: TestClient):
        """Test GET /api/v1/trends/trending/{category} endpoint."""
        with patch('database.models.trend_model.TrendRepository.get_trending_by_category') as mock_trending:
            mock_trending.return_value = [
                {
                    "id": "trend-1",
                    "keyword": "AI Technology",
                    "category": "Technology",
                    "score": 0.95
                },
                {
                    "id": "trend-2", 
                    "keyword": "Machine Learning",
                    "category": "Technology",
                    "score": 0.88
                }
            ]
            
            response = client.get("/api/v1/trends/trending/Technology")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "trends" in data
            assert len(data["trends"]) == 2
            assert data["trends"][0]["score"] >= data["trends"][1]["score"]  # Ordered by score
    
    def test_get_trend_statistics(self, client: TestClient):
        """Test GET /api/v1/trends/statistics endpoint."""
        with patch('database.models.trend_model.TrendRepository.get_trend_statistics') as mock_stats:
            mock_stats.return_value = {
                "total_trends": 100,
                "trends_by_category": {"Technology": 40, "Health": 30, "Business": 30},
                "trends_by_status": {"pending": 20, "approved": 60, "rejected": 20},
                "trends_by_region": {"US": 50, "UK": 25, "CA": 25}
            }
            
            response = client.get("/api/v1/trends/statistics")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["total_trends"] == 100
            assert "trends_by_category" in data
            assert "trends_by_status" in data
            assert "trends_by_region" in data
    
    @patch('api.v1.trends.get_current_user')
    def test_approve_trend(self, mock_auth, client: TestClient):
        """Test POST /api/v1/trends/{trend_id}/approve endpoint."""
        mock_auth.return_value = {"id": "user-id", "username": "testuser"}
        
        with patch('database.models.trend_model.TrendRepository.update') as mock_update:
            mock_update.return_value = True
            
            response = client.post(
                "/api/v1/trends/test-id/approve",
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "approved" in data["message"].lower()
    
    @patch('api.v1.trends.get_current_user')
    def test_reject_trend(self, mock_auth, client: TestClient):
        """Test POST /api/v1/trends/{trend_id}/reject endpoint."""
        mock_auth.return_value = {"id": "user-id", "username": "testuser"}
        
        rejection_data = {
            "reason": "Not relevant to our platform"
        }
        
        with patch('database.models.trend_model.TrendRepository.update') as mock_update:
            mock_update.return_value = True
            
            response = client.post(
                "/api/v1/trends/test-id/reject",
                json=rejection_data,
                headers={"Authorization": "Bearer test-token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "rejected" in data["message"].lower()
    
    def test_rate_limiting(self, client: TestClient):
        """Test API rate limiting (if implemented)."""
        # This test would depend on your rate limiting implementation
        # For now, just test that multiple requests don't fail
        for _ in range(5):
            response = client.get("/api/v1/trends")
            assert response.status_code == 200
    
    def test_cors_headers(self, client: TestClient):
        """Test CORS headers are present."""
        response = client.get("/api/v1/trends")
        
        # Check for CORS headers (if configured)
        # This depends on your CORS configuration
        assert response.status_code == 200
    
    def test_content_type_validation(self, client: TestClient):
        """Test content type validation for POST requests."""
        with patch('api.v1.trends.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": "user-id"}
            
            # Send invalid content type
            response = client.post(
                "/api/v1/trends",
                data="invalid data",
                headers={
                    "Authorization": "Bearer test-token",
                    "Content-Type": "text/plain"
                }
            )
            
            assert response.status_code == 422  # Unprocessable Entity
    
    def test_large_payload_handling(self, client: TestClient):
        """Test handling of large payloads."""
        with patch('api.v1.trends.get_current_user') as mock_auth:
            mock_auth.return_value = {"id": "user-id"}
            
            # Create a large payload
            large_data = {
                "keyword": "Test Trend",
                "category": "Technology",
                "region": "US",
                "source": "test",
                "raw_data": {"large_field": "x" * 10000}  # Large field
            }
            
            response = client.post(
                "/api/v1/trends",
                json=large_data,
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Should handle large payloads gracefully
            assert response.status_code in [201, 413, 422]  # Created, Payload Too Large, or Validation Error
