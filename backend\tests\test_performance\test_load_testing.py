"""
Performance and load testing for the Trend Platform.

Tests system performance under various load conditions,
including API endpoints, database operations, and concurrent processing.
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch, AsyncMock
import statistics

from fastapi.testclient import TestClient
from httpx import AsyncClient


@pytest.mark.slow
@pytest.mark.performance
class TestAPIPerformance:
    """Performance tests for API endpoints."""
    
    @pytest.mark.asyncio
    async def test_trends_list_performance(self, async_client: AsyncClient):
        """Test trends list endpoint performance."""
        # Warm up
        await async_client.get("/api/v1/trends")
        
        # Measure response times
        response_times = []
        for _ in range(10):
            start_time = time.time()
            response = await async_client.get("/api/v1/trends")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        # Performance assertions
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 0.5  # Average under 500ms
        assert max_response_time < 1.0  # Max under 1 second
        assert all(t < 2.0 for t in response_times)  # All under 2 seconds
    
    @pytest.mark.asyncio
    async def test_concurrent_api_requests(self, async_client: AsyncClient):
        """Test API performance under concurrent load."""
        
        async def make_request():
            response = await async_client.get("/api/v1/trends")
            return response.status_code, response.elapsed.total_seconds()
        
        # Run 20 concurrent requests
        start_time = time.time()
        tasks = [make_request() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        status_codes = [r[0] for r in results]
        response_times = [r[1] for r in results if r[1] is not None]
        
        # Performance assertions
        assert all(code == 200 for code in status_codes)  # All successful
        assert total_time < 5.0  # Complete within 5 seconds
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            assert avg_response_time < 1.0  # Average under 1 second
    
    @pytest.mark.asyncio
    async def test_search_performance(self, async_client: AsyncClient):
        """Test search endpoint performance."""
        search_queries = [
            "technology", "artificial intelligence", "machine learning",
            "blockchain", "cryptocurrency", "web development"
        ]
        
        response_times = []
        
        for query in search_queries:
            start_time = time.time()
            response = await async_client.get(f"/api/v1/trends/search?q={query}")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        # Search should be fast
        avg_response_time = statistics.mean(response_times)
        assert avg_response_time < 0.3  # Average under 300ms
    
    def test_api_memory_usage(self, client: TestClient):
        """Test API memory usage under load."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make many requests
        for _ in range(100):
            response = client.get("/api/v1/trends")
            assert response.status_code == 200
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable
        assert memory_increase < 50  # Less than 50MB increase


@pytest.mark.slow
@pytest.mark.performance
class TestDatabasePerformance:
    """Performance tests for database operations."""
    
    @pytest.mark.asyncio
    async def test_bulk_trend_insertion(self, trend_repo):
        """Test bulk trend insertion performance."""
        trends_data = []
        for i in range(100):
            trend_data = {
                "keyword": f"Performance Test Trend {i}",
                "slug": f"performance-test-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "performance_test",
                "search_volume": 1000 + i,
                "growth_rate": 0.1 + (i * 0.001)
            }
            trends_data.append(trend_data)
        
        # Measure insertion time
        start_time = time.time()
        
        # Insert trends concurrently
        tasks = [trend_repo.create(trend_data) for trend_data in trends_data]
        trend_ids = await asyncio.gather(*tasks)
        
        insertion_time = time.time() - start_time
        
        # Performance assertions
        assert len(trend_ids) == 100
        assert insertion_time < 10.0  # Should complete within 10 seconds
        assert all(trend_id is not None for trend_id in trend_ids)
    
    @pytest.mark.asyncio
    async def test_pagination_performance(self, trend_repo):
        """Test pagination performance with large datasets."""
        # First create a large dataset
        trends_data = []
        for i in range(500):
            trend_data = {
                "keyword": f"Pagination Test {i}",
                "slug": f"pagination-test-{i}",
                "category": "Technology",
                "region": "US",
                "source": "pagination_test"
            }
            trends_data.append(trend_data)
        
        # Insert in batches for better performance
        batch_size = 50
        for i in range(0, len(trends_data), batch_size):
            batch = trends_data[i:i + batch_size]
            tasks = [trend_repo.create(trend_data) for trend_data in batch]
            await asyncio.gather(*tasks)
        
        # Test pagination performance
        page_times = []
        for page in range(1, 11):  # Test first 10 pages
            start_time = time.time()
            result = await trend_repo.list_with_pagination(page=page, page_size=20)
            page_time = time.time() - start_time
            
            page_times.append(page_time)
            assert len(result["data"]) <= 20
        
        # Pagination should be consistently fast
        avg_page_time = statistics.mean(page_times)
        assert avg_page_time < 0.1  # Average under 100ms per page
        assert max(page_times) < 0.5  # No page takes more than 500ms
    
    @pytest.mark.asyncio
    async def test_search_performance_large_dataset(self, trend_repo):
        """Test search performance with large dataset."""
        # Create trends with searchable keywords
        search_terms = ["AI", "machine", "learning", "blockchain", "crypto", "web", "mobile", "cloud"]
        
        trends_data = []
        for i in range(200):
            term = search_terms[i % len(search_terms)]
            trend_data = {
                "keyword": f"{term} Technology Trend {i}",
                "slug": f"{term.lower()}-technology-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "search_test"
            }
            trends_data.append(trend_data)
        
        # Insert trends
        tasks = [trend_repo.create(trend_data) for trend_data in trends_data]
        await asyncio.gather(*tasks)
        
        # Test search performance
        search_times = []
        for term in search_terms:
            start_time = time.time()
            results = await trend_repo.search_trends(term)
            search_time = time.time() - start_time
            
            search_times.append(search_time)
            assert len(results) > 0  # Should find results
        
        # Search should be fast even with large dataset
        avg_search_time = statistics.mean(search_times)
        assert avg_search_time < 0.2  # Average under 200ms


@pytest.mark.slow
@pytest.mark.performance
class TestCeleryPerformance:
    """Performance tests for Celery task processing."""
    
    @pytest.mark.asyncio
    async def test_concurrent_task_processing(self, mock_celery_task):
        """Test concurrent Celery task processing performance."""
        
        # Mock task execution
        async def mock_task_execution():
            await asyncio.sleep(0.1)  # Simulate work
            return {"success": True, "duration": 0.1}
        
        # Simulate 20 concurrent tasks
        start_time = time.time()
        tasks = [mock_task_execution() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Should process concurrently, not sequentially
        assert total_time < 1.0  # Much less than 20 * 0.1 = 2 seconds
        assert len(results) == 20
        assert all(r["success"] for r in results)
    
    @pytest.mark.asyncio
    async def test_task_queue_throughput(self):
        """Test task queue throughput."""
        
        # Simulate high-throughput task processing
        task_count = 100
        batch_size = 10
        
        start_time = time.time()
        
        for batch_start in range(0, task_count, batch_size):
            batch_tasks = []
            for i in range(batch_start, min(batch_start + batch_size, task_count)):
                # Simulate task creation and execution
                task = asyncio.create_task(asyncio.sleep(0.01))
                batch_tasks.append(task)
            
            await asyncio.gather(*batch_tasks)
        
        total_time = time.time() - start_time
        throughput = task_count / total_time
        
        # Should achieve reasonable throughput
        assert throughput > 50  # More than 50 tasks per second
        assert total_time < 5.0  # Complete within 5 seconds


@pytest.mark.slow
@pytest.mark.performance
class TestContentGenerationPerformance:
    """Performance tests for content generation."""
    
    @pytest.mark.asyncio
    async def test_batch_content_generation_performance(self, mock_openai_client):
        """Test batch content generation performance."""
        
        # Mock fast AI responses
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Fast generated content"))],
            usage=AsyncMock(total_tokens=100)
        )
        
        from generator.ai_services import OpenAIService
        from generator.ai_services import AIContentGenerator
        
        openai_service = OpenAIService(api_key="test-key")
        openai_service.client = mock_openai_client
        
        generator = AIContentGenerator(openai_service, {})
        
        # Generate content for multiple trends
        trend_data = [
            {"keyword": f"Test Trend {i}", "category": "Technology", "region": "US"}
            for i in range(10)
        ]
        
        start_time = time.time()
        
        # Generate content concurrently
        tasks = [
            generator.generate_article(**trend)
            for trend in trend_data
        ]
        results = await asyncio.gather(*tasks)
        
        generation_time = time.time() - start_time
        
        # Performance assertions
        assert len(results) == 10
        assert generation_time < 30.0  # Should complete within 30 seconds
        assert all(r["title"] for r in results)  # All should have titles
    
    @pytest.mark.asyncio
    async def test_content_generation_memory_efficiency(self, mock_openai_client):
        """Test content generation memory efficiency."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Mock AI service
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Generated content"))],
            usage=AsyncMock(total_tokens=100)
        )
        
        from generator.ai_services import OpenAIService, AIContentGenerator
        
        openai_service = OpenAIService(api_key="test-key")
        openai_service.client = mock_openai_client
        generator = AIContentGenerator(openai_service, {})
        
        # Generate many articles
        for i in range(50):
            await generator.generate_article(
                keyword=f"Memory Test {i}",
                category="Technology",
                region="US"
            )
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage should be reasonable
        assert memory_increase < 100  # Less than 100MB increase


@pytest.mark.slow
@pytest.mark.performance
class TestSystemIntegrationPerformance:
    """Performance tests for complete system integration."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow_performance(
        self,
        trend_repo,
        content_repo,
        deployment_repo,
        mock_openai_client
    ):
        """Test end-to-end workflow performance."""
        
        # Mock AI responses
        mock_openai_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Fast content"))],
            usage=AsyncMock(total_tokens=50)
        )
        
        # Create test trends
        trend_data = [
            {
                "keyword": f"E2E Test Trend {i}",
                "slug": f"e2e-test-trend-{i}",
                "category": "Technology",
                "region": "US",
                "source": "e2e_test",
                "status": "approved"
            }
            for i in range(5)
        ]
        
        start_time = time.time()
        
        # Step 1: Create trends
        trend_creation_start = time.time()
        trend_tasks = [trend_repo.create(trend) for trend in trend_data]
        trend_ids = await asyncio.gather(*trend_tasks)
        trend_creation_time = time.time() - trend_creation_start
        
        # Step 2: Generate content
        content_generation_start = time.time()
        content_tasks = []
        for trend_id in trend_ids:
            content_data = {
                "trend_id": trend_id,
                "title": f"Article for {trend_id}",
                "description": "Test description",
                "body": "Test content body",
                "word_count": 100
            }
            content_tasks.append(content_repo.create(content_data))
        
        content_ids = await asyncio.gather(*content_tasks)
        content_generation_time = time.time() - content_generation_start
        
        # Step 3: Create deployments
        deployment_start = time.time()
        deployment_tasks = []
        for i, trend_id in enumerate(trend_ids):
            deployment_data = {
                "trend_id": trend_id,
                "content_id": content_ids[i],
                "status": "pending"
            }
            deployment_tasks.append(deployment_repo.create(deployment_data))
        
        deployment_ids = await asyncio.gather(*deployment_tasks)
        deployment_time = time.time() - deployment_start
        
        total_time = time.time() - start_time
        
        # Performance assertions
        assert len(trend_ids) == 5
        assert len(content_ids) == 5
        assert len(deployment_ids) == 5
        
        # Individual step performance
        assert trend_creation_time < 2.0  # Trend creation under 2s
        assert content_generation_time < 3.0  # Content creation under 3s
        assert deployment_time < 2.0  # Deployment creation under 2s
        
        # Total workflow performance
        assert total_time < 10.0  # Complete workflow under 10s
        
        # Verify data integrity
        for trend_id in trend_ids:
            trend = await trend_repo.get_by_id(trend_id)
            assert trend is not None
            
            content = await content_repo.get_by_trend_id(trend_id)
            assert content is not None
            
            deployment = await deployment_repo.get_by_trend_id(trend_id)
            assert deployment is not None
