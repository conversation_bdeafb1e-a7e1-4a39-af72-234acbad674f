#!/usr/bin/env python3
"""
Test runner script for the Trend Platform.

Provides convenient commands for running different types of tests
with appropriate configurations and reporting.
"""

import sys
import subprocess
import argparse
import os
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description or ' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"Command not found: {cmd[0]}")
        return False


def setup_test_environment():
    """Setup test environment and dependencies."""
    print("Setting up test environment...")
    
    # Install test dependencies
    test_deps = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0",
        "pytest-cov>=4.0.0",
        "pytest-timeout>=2.1.0",
        "httpx>=0.25.0",
        "factory-boy>=3.3.0",
        "coverage>=7.0.0"
    ]
    
    cmd = [sys.executable, "-m", "pip", "install"] + test_deps
    return run_command(cmd, "Installing test dependencies")


def run_unit_tests(verbose=False, coverage=True):
    """Run unit tests."""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "unit"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])
    
    return run_command(cmd, "Running unit tests")


def run_integration_tests(verbose=False):
    """Run integration tests."""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "integration"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running integration tests")


def run_e2e_tests(verbose=False):
    """Run end-to-end tests."""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "e2e"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running end-to-end tests")


def run_api_tests(verbose=False):
    """Run API tests."""
    cmd = ["python", "-m", "pytest", "tests/test_api/", "-v"]
    
    if verbose:
        cmd.append("-vv")
    
    return run_command(cmd, "Running API tests")


def run_database_tests(verbose=False):
    """Run database tests."""
    cmd = ["python", "-m", "pytest", "tests/test_database/", "-v"]
    
    if verbose:
        cmd.append("-vv")
    
    return run_command(cmd, "Running database tests")


def run_scraper_tests(verbose=False):
    """Run scraper tests."""
    cmd = ["python", "-m", "pytest", "tests/test_scraper/", "-v"]
    
    if verbose:
        cmd.append("-vv")
    
    return run_command(cmd, "Running scraper tests")


def run_generator_tests(verbose=False):
    """Run content generation tests."""
    cmd = ["python", "-m", "pytest", "tests/test_generator/", "-v"]
    
    if verbose:
        cmd.append("-vv")
    
    return run_command(cmd, "Running content generation tests")


def run_deployment_tests(verbose=False):
    """Run deployment tests."""
    cmd = ["python", "-m", "pytest", "tests/test_deployment/", "-v"]
    
    if verbose:
        cmd.append("-vv")
    
    return run_command(cmd, "Running deployment tests")


def run_all_tests(verbose=False, coverage=True, skip_slow=False):
    """Run all tests."""
    cmd = ["python", "-m", "pytest", "tests/"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])
    
    if skip_slow:
        cmd.extend(["-m", "not slow"])
    
    return run_command(cmd, "Running all tests")


def run_fast_tests(verbose=False):
    """Run fast tests only (excluding slow and external)."""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "not slow and not external"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running fast tests")


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test function."""
    cmd = ["python", "-m", "pytest", test_path]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, f"Running specific test: {test_path}")


def generate_coverage_report():
    """Generate detailed coverage report."""
    print("\nGenerating coverage report...")
    
    # Generate HTML report
    html_success = run_command(
        ["python", "-m", "coverage", "html"],
        "Generating HTML coverage report"
    )
    
    # Generate XML report (for CI/CD)
    xml_success = run_command(
        ["python", "-m", "coverage", "xml"],
        "Generating XML coverage report"
    )
    
    if html_success:
        print(f"\nHTML coverage report generated: {Path.cwd() / 'htmlcov' / 'index.html'}")
    
    if xml_success:
        print(f"XML coverage report generated: {Path.cwd() / 'coverage.xml'}")
    
    return html_success and xml_success


def lint_code():
    """Run code linting."""
    print("Running code linting...")
    
    # Run flake8
    flake8_success = run_command(
        ["python", "-m", "flake8", ".", "--max-line-length=100", "--exclude=venv,env,migrations"],
        "Running flake8 linting"
    )
    
    # Run black check
    black_success = run_command(
        ["python", "-m", "black", "--check", "--diff", "."],
        "Running black formatting check"
    )
    
    # Run isort check
    isort_success = run_command(
        ["python", "-m", "isort", "--check-only", "--diff", "."],
        "Running isort import sorting check"
    )
    
    return flake8_success and black_success and isort_success


def format_code():
    """Format code with black and isort."""
    print("Formatting code...")
    
    # Run black
    black_success = run_command(
        ["python", "-m", "black", "."],
        "Formatting code with black"
    )
    
    # Run isort
    isort_success = run_command(
        ["python", "-m", "isort", "."],
        "Sorting imports with isort"
    )
    
    return black_success and isort_success


def check_security():
    """Run security checks."""
    print("Running security checks...")
    
    # Run bandit
    bandit_success = run_command(
        ["python", "-m", "bandit", "-r", ".", "-x", "tests,venv,env"],
        "Running bandit security check"
    )
    
    # Run safety
    safety_success = run_command(
        ["python", "-m", "safety", "check"],
        "Running safety dependency check"
    )
    
    return bandit_success and safety_success


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Trend Platform Test Runner")
    parser.add_argument("--setup", action="store_true", help="Setup test environment")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run end-to-end tests")
    parser.add_argument("--api", action="store_true", help="Run API tests")
    parser.add_argument("--database", action="store_true", help="Run database tests")
    parser.add_argument("--scraper", action="store_true", help="Run scraper tests")
    parser.add_argument("--generator", action="store_true", help="Run generator tests")
    parser.add_argument("--deployment", action="store_true", help="Run deployment tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--fast", action="store_true", help="Run fast tests only")
    parser.add_argument("--test", type=str, help="Run specific test file or function")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--format", action="store_true", help="Format code")
    parser.add_argument("--security", action="store_true", help="Run security checks")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--no-coverage", action="store_true", help="Skip coverage reporting")
    parser.add_argument("--skip-slow", action="store_true", help="Skip slow tests")
    
    args = parser.parse_args()
    
    # Set working directory to backend
    os.chdir(Path(__file__).parent)
    
    success = True
    
    if args.setup:
        success &= setup_test_environment()
    
    if args.unit:
        success &= run_unit_tests(args.verbose, not args.no_coverage)
    
    if args.integration:
        success &= run_integration_tests(args.verbose)
    
    if args.e2e:
        success &= run_e2e_tests(args.verbose)
    
    if args.api:
        success &= run_api_tests(args.verbose)
    
    if args.database:
        success &= run_database_tests(args.verbose)
    
    if args.scraper:
        success &= run_scraper_tests(args.verbose)
    
    if args.generator:
        success &= run_generator_tests(args.verbose)
    
    if args.deployment:
        success &= run_deployment_tests(args.verbose)
    
    if args.all:
        success &= run_all_tests(args.verbose, not args.no_coverage, args.skip_slow)
    
    if args.fast:
        success &= run_fast_tests(args.verbose)
    
    if args.test:
        success &= run_specific_test(args.test, args.verbose)
    
    if args.coverage:
        success &= generate_coverage_report()
    
    if args.lint:
        success &= lint_code()
    
    if args.format:
        success &= format_code()
    
    if args.security:
        success &= check_security()
    
    # If no specific command was given, run fast tests
    if not any([
        args.setup, args.unit, args.integration, args.e2e, args.api,
        args.database, args.scraper, args.generator, args.deployment,
        args.all, args.fast, args.test, args.coverage, args.lint,
        args.format, args.security
    ]):
        print("No specific test command given. Running fast tests...")
        success &= run_fast_tests(args.verbose)
    
    if success:
        print("\n✅ All operations completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some operations failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
