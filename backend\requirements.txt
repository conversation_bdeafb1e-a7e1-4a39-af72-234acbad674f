# Core FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database and ORM
asyncpg>=0.29.0
supabase>=2.0.0

# Task queue and caching
celery>=5.3.0
redis>=5.0.0

# Web scraping and HTTP clients
pytrends>=4.9.0
aiohttp>=3.9.0
beautifulsoup4>=4.12.0
requests>=2.31.0

# Template engine and content processing
jinja2>=3.1.0
python-frontmatter>=1.0.0
pillow>=10.0.0
python-multipart>=0.0.6
markdown>=3.5.0
aiofiles>=23.2.0

# AI and content generation
openai>=1.3.0
tiktoken>=0.5.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0
pytest-timeout>=2.1.0
pytest-xdist>=3.3.0
factory-boy>=3.3.0
coverage>=7.0.0
httpx>=0.25.0

# Code quality
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
bandit>=1.7.0
safety>=2.3.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# Data validation and serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0

# String matching and text processing
fuzzywuzzy>=0.18.0
python-levenshtein>=0.23.0

# Git operations
gitpython>=3.1.0

# Environment and configuration
python-dotenv>=1.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx>=0.25.0

# Development tools
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.7.0
