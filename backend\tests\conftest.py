"""
Pytest configuration and shared fixtures for the Trend Platform test suite.

Provides common fixtures, test database setup, and mock configurations
for consistent testing across all components.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock
import tempfile
import shutil
from pathlib import Path

# FastAPI and HTTP testing
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Database testing
import asyncpg
from database.connection import get_database_pool, create_database_pool
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from database.models.deployment_model import DeploymentRepository

# Application imports
from app.main import app
from app.config import settings

# Test configuration
TEST_DATABASE_URL = "postgresql://test_user:test_pass@localhost:5432/test_trend_platform"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_db_pool():
    """Create a test database connection pool."""
    # Create test database pool
    pool = await create_database_pool(TEST_DATABASE_URL)
    
    # Run migrations on test database
    await setup_test_database(pool)
    
    yield pool
    
    # Cleanup
    await pool.close()


async def setup_test_database(pool):
    """Setup test database with required tables."""
    async with pool.acquire() as conn:
        # Create test tables (simplified versions)
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS trends (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                keyword VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                category VARCHAR(100) NOT NULL,
                region VARCHAR(10) NOT NULL,
                source VARCHAR(100) NOT NULL,
                search_volume INTEGER,
                growth_rate FLOAT,
                score FLOAT DEFAULT 0.0,
                status VARCHAR(50) DEFAULT 'pending',
                raw_data JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS content (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                trend_id UUID NOT NULL REFERENCES trends(id) ON DELETE CASCADE,
                title VARCHAR(500) NOT NULL,
                description TEXT,
                body TEXT NOT NULL,
                meta_tags JSONB DEFAULT '{}',
                hero_image_url VARCHAR(1000),
                code_snippet TEXT,
                code_language VARCHAR(50),
                word_count INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS deployments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                trend_id UUID NOT NULL REFERENCES trends(id) ON DELETE CASCADE,
                content_id UUID REFERENCES content(id) ON DELETE SET NULL,
                status VARCHAR(50) NOT NULL DEFAULT 'pending',
                progress INTEGER DEFAULT 0,
                custom_domain VARCHAR(255),
                deploy_url VARCHAR(500),
                dns_configured BOOLEAN DEFAULT FALSE,
                coolify_app_uuid VARCHAR(255),
                coolify_deployment_uuid VARCHAR(255),
                git_commit_sha VARCHAR(40),
                build_duration INTEGER,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)


@pytest.fixture
async def db_pool(test_db_pool):
    """Provide database pool for individual tests."""
    # Clean up tables before each test
    async with test_db_pool.acquire() as conn:
        await conn.execute("TRUNCATE trends, content, deployments CASCADE")
    
    yield test_db_pool


@pytest.fixture
async def trend_repo(db_pool):
    """Provide TrendRepository instance for testing."""
    return TrendRepository(db_pool)


@pytest.fixture
async def content_repo(db_pool):
    """Provide ContentRepository instance for testing."""
    return ContentRepository(db_pool)


@pytest.fixture
async def deployment_repo(db_pool):
    """Provide DeploymentRepository instance for testing."""
    return DeploymentRepository(db_pool)


@pytest.fixture
def client():
    """Provide FastAPI test client."""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """Provide async HTTP client for testing."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def temp_dir():
    """Provide temporary directory for file operations."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    mock_client = AsyncMock()
    
    # Mock text generation
    mock_client.chat.completions.create.return_value = AsyncMock(
        choices=[
            AsyncMock(
                message=AsyncMock(content="Generated test content"),
                finish_reason="stop"
            )
        ],
        usage=AsyncMock(
            total_tokens=100,
            prompt_tokens=50,
            completion_tokens=50
        ),
        model="gpt-3.5-turbo"
    )
    
    # Mock image generation
    mock_client.images.generate.return_value = AsyncMock(
        data=[
            AsyncMock(url="https://example.com/test-image.jpg")
        ]
    )
    
    return mock_client


@pytest.fixture
def mock_coolify_client():
    """Mock Coolify client for testing."""
    mock_client = AsyncMock()
    
    # Mock application creation
    mock_client.create_application.return_value = {
        "id": "test-app-id",
        "name": "test-app",
        "default_domain": "test-app.coolify.io"
    }
    
    # Mock deployment
    mock_client.deploy_application.return_value = AsyncMock(
        id="test-deployment-id",
        application_id="test-app-id",
        status="pending",
        commit_sha="abc123"
    )
    
    # Mock deployment status
    mock_client.get_deployment_status.return_value = AsyncMock(
        id="test-deployment-id",
        application_id="test-app-id",
        status="success",
        deploy_url="https://test-app.coolify.io"
    )
    
    return mock_client


@pytest.fixture
def mock_cloudflare_client():
    """Mock Cloudflare client for testing."""
    mock_client = AsyncMock()
    
    # Mock DNS record creation
    mock_client.create_dns_record.return_value = {
        "success": True,
        "record_id": "test-record-id",
        "name": "test.example.com",
        "content": "target.example.com",
        "type": "CNAME"
    }
    
    # Mock DNS record retrieval
    mock_client.get_dns_record.return_value = {
        "id": "test-record-id",
        "name": "test.example.com",
        "content": "target.example.com",
        "type": "CNAME"
    }
    
    return mock_client


@pytest.fixture
def sample_trend_data():
    """Provide sample trend data for testing."""
    return {
        "keyword": "Test Trend",
        "slug": "test-trend",
        "category": "Technology",
        "region": "US",
        "source": "test_source",
        "search_volume": 1000,
        "growth_rate": 0.15,
        "score": 0.8,
        "status": "pending",
        "raw_data": {"test": "data"}
    }


@pytest.fixture
def sample_content_data():
    """Provide sample content data for testing."""
    return {
        "title": "Test Article Title",
        "description": "Test article description",
        "body": "This is test article content with multiple paragraphs.",
        "meta_tags": {"keywords": "test, article"},
        "hero_image_url": "https://example.com/test-image.jpg",
        "word_count": 10
    }


@pytest.fixture
def sample_deployment_data():
    """Provide sample deployment data for testing."""
    return {
        "status": "pending",
        "progress": 0,
        "custom_domain": "test.example.com",
        "deploy_url": "https://test-app.coolify.io",
        "dns_configured": False
    }


@pytest.fixture
def mock_celery_task():
    """Mock Celery task for testing."""
    mock_task = MagicMock()
    mock_task.delay.return_value = MagicMock(id="test-task-id")
    mock_task.apply_async.return_value = MagicMock(id="test-task-id")
    return mock_task


@pytest.fixture(autouse=True)
def mock_settings(monkeypatch):
    """Mock settings for testing environment."""
    monkeypatch.setattr(settings, "database_url", TEST_DATABASE_URL)
    monkeypatch.setattr(settings, "environment", "testing")
    monkeypatch.setattr(settings, "openai_api_key", "test-openai-key")
    monkeypatch.setattr(settings, "coolify_api_url", "https://test-coolify.com")
    monkeypatch.setattr(settings, "coolify_api_token", "test-coolify-token")
    monkeypatch.setattr(settings, "cloudflare_api_token", "test-cf-token")
    monkeypatch.setattr(settings, "cloudflare_zone_id", "test-zone-id")


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )


# Test data factories
class TrendFactory:
    """Factory for creating test trend data."""
    
    @staticmethod
    def create(**kwargs):
        """Create trend data with optional overrides."""
        default_data = {
            "keyword": "Test Trend",
            "slug": "test-trend",
            "category": "Technology",
            "region": "US",
            "source": "test_source",
            "search_volume": 1000,
            "growth_rate": 0.15,
            "score": 0.8,
            "status": "pending"
        }
        default_data.update(kwargs)
        return default_data


class ContentFactory:
    """Factory for creating test content data."""
    
    @staticmethod
    def create(**kwargs):
        """Create content data with optional overrides."""
        default_data = {
            "title": "Test Article",
            "description": "Test description",
            "body": "Test content body",
            "word_count": 10
        }
        default_data.update(kwargs)
        return default_data


class DeploymentFactory:
    """Factory for creating test deployment data."""
    
    @staticmethod
    def create(**kwargs):
        """Create deployment data with optional overrides."""
        default_data = {
            "status": "pending",
            "progress": 0,
            "dns_configured": False
        }
        default_data.update(kwargs)
        return default_data
